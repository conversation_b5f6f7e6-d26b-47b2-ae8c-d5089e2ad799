#!/usr/bin/env python
"""
Test de l'API de validation backend pour MULTIPLY et DIVIDE
"""

import os
import sys
import django

# Configuration Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from myapp.services.scenario_backend_validation_service import ScenarioBackendValidationService


def test_backend_validation_api():
    """Test de l'API de validation backend"""
    print("🧪 Test de l'API de validation backend pour MULTIPLY")
    
    # Contenu du scénario exactement comme envoyé par le frontend
    scenario_content = """TRANSFERT {INIT 3x3; EDIT 7 [0,0]; EDIT 7 [0,2]; EDIT 7 [1,0]; EDIT 7 [1,2]; EDIT 7 [2,0]; EDIT 7 [2,1]}
RESIZE 6x6
COPY (COLOR 7 [0,0 2,2])
MULTIPLY 2 [0,0 2,2]
PASTE [0,0]
END"""
    
    # Paramètres comme envoyés par le frontend
    task_id = '007bbfb7'
    subset = 'training'
    test_index = 0
    
    print(f"📋 Scénario à valider:")
    for i, line in enumerate(scenario_content.split('\n'), 1):
        print(f"  {i}. {line}")
    
    # Utiliser le service de validation backend
    validation_service = ScenarioBackendValidationService()
    
    print(f"\n🔍 Validation avec task_id={task_id}, subset={subset}, test_index={test_index}")
    
    result = validation_service.validate_scenario(
        subset, task_id, scenario_content, test_index
    )
    
    print(f"\n📊 Résultat de validation:")
    print(f"  - Valid: {result.get('valid', False)}")
    print(f"  - Execution success: {result.get('execution_success', False)}")
    print(f"  - Grid matches: {result.get('grid_matches_expected', False)}")
    print(f"  - Stage: {result.get('stage', 'N/A')}")
    print(f"  - Command count: {result.get('command_count', 0)}")
    
    if result.get('errors'):
        print(f"\n❌ Erreurs ({len(result['errors'])}):")
        for i, error in enumerate(result['errors'], 1):
            print(f"  {i}. {error}")
    
    if result.get('conversion_errors'):
        print(f"\n⚠️ Erreurs de conversion ({len(result['conversion_errors'])}):")
        for i, error in enumerate(result['conversion_errors'], 1):
            print(f"  {i}. {error}")
    
    # Vérifier spécifiquement les erreurs liées à MULTIPLY
    multiply_errors = []
    for error in result.get('errors', []):
        if 'MULTIPLY' in str(error):
            multiply_errors.append(error)
    
    if multiply_errors:
        print(f"\n🔍 Erreurs spécifiques à MULTIPLY ({len(multiply_errors)}):")
        for error in multiply_errors:
            print(f"  - {error}")
    else:
        print(f"\n✅ Aucune erreur spécifique à MULTIPLY trouvée")
    
    return result.get('valid', False)


def test_simple_multiply_backend():
    """Test simple avec juste MULTIPLY"""
    print("\n🧪 Test simple MULTIPLY avec validation backend")
    
    simple_scenario = """INIT 6x6
EDIT 7 [0,0]
EDIT 7 [0,2]
EDIT 7 [1,0]
EDIT 7 [1,2]
EDIT 7 [2,0]
EDIT 7 [2,1]
MULTIPLY 2 [0,0 2,2]
END"""
    
    validation_service = ScenarioBackendValidationService()
    
    result = validation_service.validate_scenario(
        'training', '007bbfb7', simple_scenario, 0
    )
    
    print(f"Résultat validation simple: {result.get('valid', False)}")
    print(f"Execution success: {result.get('execution_success', False)}")
    
    if result.get('errors'):
        print(f"Erreurs: {result['errors']}")
    
    return result.get('valid', False)


if __name__ == "__main__":
    print("=== Test API de validation backend ===")
    
    success1 = test_simple_multiply_backend()
    success2 = test_backend_validation_api()
    
    print(f"\n📊 Résumé:")
    print(f"  - Test simple: {'✅' if success1 else '❌'}")
    print(f"  - Test complet: {'✅' if success2 else '❌'}")
    
    if success1 and success2:
        print("\n🎉 Validation backend fonctionne pour MULTIPLY!")
    else:
        print("\n💥 Problèmes détectés dans la validation backend")
