"""
Service de validation robuste des scénarios optimisés.

Ce service intègre tous les composants de validation :
- Décompression des commandes optimisées
- Conversion des formats
- Validation et exécution des commandes
- Comparaison avec les résultats attendus
"""

import json
import logging
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path

from ai.command_decompressor import CommandDecompressor, decompress_scenario_commands
from command_system.command_validator import CommandValidator
from command_system.command_executor import CommandExecutor

logger = logging.getLogger(__name__)


class ScenarioValidationService:
    """
    Service complet de validation des scénarios optimisés.
    
    Pipeline de validation :
    1. Décompression des commandes optimisées (EDITS {...} -> EDIT individuels)
    2. Conversion des formats (frontend -> backend)
    3. Validation syntaxique
    4. Exécution et test
    5. Comparaison avec résultat attendu
    """
    
    def __init__(self):
        """Initialise le service de validation."""
        self.decompressor = CommandDecompressor()
        #self.converter = CommandFormatConverter()
        self.validator = CommandValidator()
        self.executor = CommandExecutor()
        
        # Statistiques de validation
        self.validation_stats = {
            'total_validations': 0,
            'successful_validations': 0,
            'failed_validations': 0,
            'syntax_errors': 0,
            'execution_errors': 0,
            'comparison_errors': 0
        }
    
    def validate_optimized_scenario(self, scenario_content: str, task_data: dict, 
                                  test_index: int = 0) -> Dict[str, Any]:
        """
        Pipeline complet de validation d'un scénario optimisé.
        
        Args:
            scenario_content: Contenu du scénario avec commandes optimisées
            task_data: Données de la tâche pour comparaison
            test_index: Index du test à valider (défaut: 0)
            
        Returns:
            Dictionnaire avec résultat de validation détaillé
        """
        self.validation_stats['total_validations'] += 1
        
        try:
            # Étape 1: Décompression des commandes optimisées
            original_commands = self._parse_scenario_content(scenario_content)
            if not original_commands:
                return self._create_error_result("Aucune commande trouvée dans le scénario")
            
            expanded_commands = self.decompressor.decompress_commands(original_commands)
            
            # Étape 2: Conversion des formats frontend -> backend
            # Pour l'instant, on utilise directement les commandes expandées
            backend_commands = expanded_commands.copy()
            conversion_errors = []
            
            # TODO: Implémenter la conversion frontend -> backend si nécessaire
            """for cmd in expanded_commands:
                try:
                    converted = self.converter.frontend_to_backend(cmd)
                    backend_commands.append(converted)
                except Exception as e:
                    conversion_errors.append({
                        'command': cmd,
                        'error': str(e)
                    })"""
            
            # Étape 3: Validation syntaxique
            syntax_validation = self._validate_syntax(backend_commands)
            if not syntax_validation['valid']:
                self.validation_stats['syntax_errors'] += 1
                return self._create_validation_result(
                    valid=False,
                    original_commands=original_commands,
                    expanded_commands=expanded_commands,
                    backend_commands=backend_commands,
                    errors=syntax_validation['errors'],
                    conversion_errors=conversion_errors,
                    stage='syntax_validation'
                )
            
            # Étape 4: Exécution des commandes
            execution_result = self.executor.execute_commands(backend_commands)
            if not execution_result['success']:
                self.validation_stats['execution_errors'] += 1
                # Créer un message d'erreur plus détaillé
                error_detail = execution_result.get('error', 'Erreur inconnue lors de l\'exécution')
                # S'assurer que les détails de l'erreur sont bien formatés
                if isinstance(error_detail, dict):
                    error_message = error_detail.get('message', str(error_detail))
                else:
                    error_message = str(error_detail)

                # Créer un résultat de comparaison indiquant l'échec
                failed_comparison = {
                    'matches': False,
                    'error': 'Execution failed, comparison not performed',
                    'generated_grid': execution_result.get('grid'),
                    'expected_grid': None
                }
                return self._create_validation_result(
                    valid=False,
                    original_commands=original_commands,
                    expanded_commands=expanded_commands,
                    backend_commands=backend_commands,
                    execution_result=execution_result,
                    comparison_result=failed_comparison, # Toujours inclure
                    errors=[{'stage': 'execution', 'error': error_message}],
                    conversion_errors=conversion_errors,
                    stage='execution'
                )
            
            # Étape 5: Comparaison avec résultat attendu
            comparison_result = self._compare_with_expected(
                execution_result, task_data, test_index
            )
            
            if not comparison_result['matches']:
                self.validation_stats['comparison_errors'] += 1
                return self._create_validation_result(
                    valid=False,
                    original_commands=original_commands,
                    expanded_commands=expanded_commands,
                    backend_commands=backend_commands,
                    execution_result=execution_result,
                    comparison_result=comparison_result,
                    errors=[{'stage': 'comparison', 'error': comparison_result['error']}],
                    conversion_errors=conversion_errors,
                    stage='comparison'
                )
            
            # Succès !
            self.validation_stats['successful_validations'] += 1
            return self._create_validation_result(
                valid=True,
                original_commands=original_commands,
                expanded_commands=expanded_commands,
                backend_commands=backend_commands,
                execution_result=execution_result,
                comparison_result=comparison_result,
                conversion_errors=conversion_errors,
                stage='completed'
            )
            
        except Exception as e:
            logger.error(f"Unexpected error in validation pipeline: {e}")
            self.validation_stats['failed_validations'] += 1
            return self._create_error_result(f"Erreur inattendue: {str(e)}")
    
    def _parse_scenario_content(self, content: str) -> List[str]:
        """
        Parse le contenu d'un scénario en liste de commandes.
        
        Args:
            content: Contenu du scénario
            
        Returns:
            Liste des commandes nettoyées
        """
        if not content:
            return []
        
        lines = [line.strip() for line in content.split('\n') if line.strip()]
        return [line for line in lines if not line.startswith('#')]  # Ignorer commentaires
    
    def _validate_syntax(self, commands: List[str]) -> Dict[str, Any]:
        """
        Valide la syntaxe des commandes.
        
        Args:
            commands: Liste des commandes à valider
            
        Returns:
            Résultat de validation syntaxique
        """
        errors = []
        
        for i, command in enumerate(commands):
            try:
                # Utiliser le validateur existant
                parts = command.strip().split()
                if not parts:
                    errors.append({
                        'line': i + 1,
                        'command': command,
                        'error': 'Commande vide'
                    })
                    continue
                
                # Validation basique du format
                cmd_type = parts[0].upper()
                valid_commands = [
                    'INIT', 'EDIT', 'COPY', 'PASTE', 'FILL', 'CLEAR', 'TRANSFERT', 'CUT', 'REPLACE',
                    'EDITS', 'COPYS', 'PASTES', 'FILLS', 'CLEARS', 'TRANSFERTS', 'REPLACES',
                    'FLOODFILL', 'DELETE', 'INSERT',
                    'FLIP', 'ROTATE', 'SURROUND',
                    'MULTIPLY', 'DIVIDE',  # Nouvelles commandes ajoutées
                    'RESIZE', 'END', 'EXTRACT'
                ]
                
                if cmd_type not in valid_commands:
                    errors.append({
                        'line': i + 1,
                        'command': command,
                        'error': f'Commande inconnue: {cmd_type}'
                    })
                
                # Vérifier les commandes obsolètes SELECT_COLOR et SELECT_INVERT
                if 'SELECT_COLOR' in command:
                    errors.append({
                        'line': i + 1,
                        'command': command,
                        'error': 'Commande obsolète SELECT_COLOR détectée. Utilisez le nouveau format COLOR.'
                    })
                
                if 'SELECT_INVERT' in command:
                    errors.append({
                        'line': i + 1,
                        'command': command,
                        'error': 'Commande obsolète SELECT_INVERT détectée. Utilisez le nouveau format INVERT.'
                    })
                
                    
            except Exception as e:
                errors.append({
                    'line': i + 1,
                    'command': command,
                    'error': str(e)
                })
        
        return {
            'valid': len(errors) == 0,
            'errors': errors
        }
    
    def _compare_with_expected(self, execution_result: Dict[str, Any], 
                             task_data: dict, test_index: int) -> Dict[str, Any]:
        """
        Compare le résultat d'exécution avec la sortie attendue.
        
        Args:
            execution_result: Résultat de l'exécution des commandes
            task_data: Données de la tâche
            test_index: Index du test
            
        Returns:
            Résultat de la comparaison        """
        
        try:
            # Vérifier que execution_result n'est pas None
            if execution_result is None:
                return {
                    'matches': False,
                    'error': 'Résultat d\'exécution manquant (None)'
                }
            
            # Vérifier que task_data n'est pas None
            if task_data is None:
                return {
                    'matches': False,
                    'error': 'Données de tâche manquantes (None)'
                }
            
            # Extraire la grille générée
            generated_grid = execution_result.get('grid')
            if not generated_grid:
                return {
                    'matches': False,
                    'error': 'Aucune grille générée par l\'exécution'
                }
            
            # Extraire la grille attendue
            tests = task_data.get('test', [])
            if test_index >= len(tests):
                return {
                    'matches': False,
                    'error': f'Index de test {test_index} invalide (max: {len(tests)-1})'
                }
            
            expected_output = tests[test_index].get('output')
            if not expected_output:
                return {
                    'matches': False,
                    'error': f'Pas de sortie attendue pour le test {test_index}'
                }
            
            # Comparer les dimensions
            gen_height, gen_width = len(generated_grid), len(generated_grid[0]) if generated_grid else (0, 0)
            exp_height, exp_width = len(expected_output), len(expected_output[0]) if expected_output else (0, 0)
            
            if gen_height != exp_height or gen_width != exp_width:
                return {
                    'matches': False,
                    'error': f'Dimensions différentes: généré {gen_height}x{gen_width}, attendu {exp_height}x{exp_width}',
                    'generated_grid': generated_grid,
                    'expected_grid': expected_output,
                    'dimensions_match': False
                }
            
            # Comparer cellule par cellule
            differences = []
            for row in range(gen_height):
                for col in range(gen_width):
                    if generated_grid[row][col] != expected_output[row][col]:
                        differences.append({
                            'row': row,
                            'col': col,
                            'generated': generated_grid[row][col],
                            'expected': expected_output[row][col]
                        })
            
            if differences:
                return {
                    'matches': False,
                    'error': f'{len(differences)} cellules différentes',
                    'generated_grid': generated_grid,
                    'expected_grid': expected_output,
                    'dimensions_match': True,
                    'differences': differences[:10]  # Limiter à 10 différences pour lisibilité
                }
            
            # Succès - grilles identiques
            return {
                'matches': True,
                'generated_grid': generated_grid,
                'expected_grid': expected_output,
                'dimensions_match': True,
                'differences': []
            }
            
        except Exception as e:
            return {
                'matches': False,
                'error': f'Erreur lors de la comparaison: {str(e)}'
            }
    
    def _create_validation_result(self, valid: bool, original_commands: List[str],
                                expanded_commands: List[str], backend_commands: List[str],
                                execution_result: Optional[Dict] = None,
                                comparison_result: Optional[Dict] = None,
                                errors: Optional[List[Dict]] = None,
                                conversion_errors: Optional[List[Dict]] = None,
                                stage: str = 'unknown') -> Dict[str, Any]:
        """
        Crée un résultat de validation standardisé.
        """
        return {
            'valid': valid,
            'stage': stage,
            'execution_success': execution_result.get('success', False) if execution_result else False,
            'grid_matches_expected': comparison_result.get('matches', False) if comparison_result else False,
            'command_stats': {
                'original_count': len(original_commands),
                'expanded_count': len(expanded_commands),
                'backend_count': len(backend_commands),
                'optimization_ratio': len(original_commands) / max(1, len(expanded_commands))
            },
            'errors': errors or [],
            'conversion_errors': conversion_errors or [],
            'debug_info': {
                'original_commands': original_commands,
                'expanded_commands': expanded_commands,
                'backend_commands': backend_commands,
                'execution_result': execution_result,
                'comparison_result': comparison_result,
                'decompressor_stats': self.decompressor.get_stats(),
                #'converter_stats': self.converter.get_conversion_stats()
            }
        }
    
    def _create_error_result(self, error_message: str) -> Dict[str, Any]:
        """
        Crée un résultat d'erreur standardisé.
        """
        return {
            'valid': False,
            'stage': 'error',
            'execution_success': False,
            'grid_matches_expected': False,
            'errors': [{'stage': 'general', 'error': error_message}],
            'command_stats': {'original_count': 0, 'expanded_count': 0, 'backend_count': 0},
            'debug_info': {
                'comparison_result': {
                    'matches': False,
                    'error': 'Validation failed before comparison',
                    'generated_grid': None,
                    'expected_grid': None
                }
            }
        }
    
    def validate_scenario_file(self, file_path: str, task_data: dict, 
                             test_index: int = 0) -> Dict[str, Any]:
        """
        Valide un fichier de scénario.
        
        Args:
            file_path: Chemin vers le fichier de scénario
            task_data: Données de la tâche
            test_index: Index du test
            
        Returns:
            Résultat de validation
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            result = self.validate_optimized_scenario(content, task_data, test_index)
            result['file_path'] = file_path
            return result
            
        except FileNotFoundError:
            return self._create_error_result(f"Fichier non trouvé: {file_path}")
        except Exception as e:
            return self._create_error_result(f"Erreur lecture fichier: {str(e)}")
    
    def get_validation_stats(self) -> Dict[str, Any]:
        """
        Retourne les statistiques de validation.
        
        Returns:
            Dictionnaire avec les statistiques
        """
        stats = self.validation_stats.copy()
        if stats['total_validations'] > 0:
            stats['success_rate'] = stats['successful_validations'] / stats['total_validations']
        else:
            stats['success_rate'] = 0.0
        
        return stats
    
    def reset_stats(self) -> None:
        """Remet à zéro les statistiques."""
        self.validation_stats = {
            'total_validations': 0,
            'successful_validations': 0,
            'failed_validations': 0,
            'syntax_errors': 0,
            'execution_errors': 0,
            'comparison_errors': 0
        }
        
        # Reset des stats des composants
        self.decompressor.reset_stats()
        #self.converter.reset_stats()


# Fonctions utilitaires pour l'utilisation externe

def validate_scenario_content(scenario_content: str, task_data: dict, 
                            test_index: int = 0) -> Dict[str, Any]:
    """
    Fonction utilitaire pour valider le contenu d'un scénario.
    
    Args:
        scenario_content: Contenu du scénario
        task_data: Données de la tâche
        test_index: Index du test
        
    Returns:
        Résultat de validation
    """
    service = ScenarioValidationService()
    return service.validate_optimized_scenario(scenario_content, task_data, test_index)


def validate_scenario_file_path(file_path: str, task_data: dict, 
                               test_index: int = 0) -> Dict[str, Any]:
    """
    Fonction utilitaire pour valider un fichier de scénario.
    
    Args:
        file_path: Chemin vers le fichier
        task_data: Données de la tâche
        test_index: Index du test
        
    Returns:
        Résultat de validation
    """
    service = ScenarioValidationService()
    return service.validate_scenario_file(file_path, task_data, test_index)