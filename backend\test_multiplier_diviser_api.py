#!/usr/bin/env python
"""
Test des nouvelles commandes MULTIPLIER et DIVISER via l'API
"""

import os
import sys
import django
import json

# Configuration Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from django.test import Client
from django.contrib.auth.models import User
from myapp.models import ARCTask


def create_test_task():
    """Crée une tâche de test"""
    task_data = {
        "train": [
            {
                "input": [[1, 2], [3, 4]],
                "output": [[1, 1, 2, 2], [1, 1, 2, 2], [3, 3, 4, 4], [3, 3, 4, 4]]
            }
        ],
        "test": [
            {
                "input": [[1, 2], [3, 4]]
            }
        ]
    }
    
    task, created = ARCTask.objects.get_or_create(
        task_id='test_multiplier_diviser',
        defaults={
            'task_data': task_data,
            'task_type': 'test',
            'difficulty': 'easy'
        }
    )
    return task


def test_multiplier_api():
    """Test de la commande MULTIPLIER via l'API"""
    print("🧪 Test API MULTIPLIER")
    
    client = Client()
    task = create_test_task()
    
    # Commandes de test
    commands = [
        "INIT 4x4",
        "EDIT 1 [0,0]",
        "EDIT 2 [0,1]", 
        "EDIT 3 [1,0]",
        "EDIT 4 [1,1]",
        "MULTIPLIER 2 [0,0 1,1]"
    ]
    
    data = {
        'task_id': task.task_id,
        'commands': '\n'.join(commands)
    }
    
    response = client.post('/api/commands/test/', data, content_type='application/json')
    
    print(f"Status: {response.status_code}")
    if response.status_code == 200:
        result = response.json()
        print(f"Succès: {result.get('success', False)}")
        if result.get('success'):
            execution_result = result.get('data', {}).get('execution_result', {})
            if execution_result.get('success'):
                grid = execution_result.get('grid')
                print("Grille résultante:")
                for row in grid:
                    print(row)
                return True
            else:
                print(f"Erreur d'exécution: {execution_result.get('error')}")
        else:
            print(f"Erreur API: {result.get('error')}")
    else:
        print(f"Erreur HTTP: {response.content}")
    
    return False


def test_diviser_api():
    """Test de la commande DIVISER via l'API"""
    print("\n🧪 Test API DIVISER")
    
    client = Client()
    task = create_test_task()
    
    # Commandes de test - créer une grille 4x4 puis la diviser
    commands = [
        "INIT 4x4",
        "EDIT 1 [0,0]", "EDIT 1 [0,1]", "EDIT 2 [0,2]", "EDIT 2 [0,3]",
        "EDIT 1 [1,0]", "EDIT 1 [1,1]", "EDIT 2 [1,2]", "EDIT 2 [1,3]",
        "EDIT 3 [2,0]", "EDIT 3 [2,1]", "EDIT 4 [2,2]", "EDIT 4 [2,3]",
        "EDIT 3 [3,0]", "EDIT 3 [3,1]", "EDIT 4 [3,2]", "EDIT 4 [3,3]",
        "DIVISER 2 [0,0 3,3]"
    ]
    
    data = {
        'task_id': task.task_id,
        'commands': '\n'.join(commands)
    }
    
    response = client.post('/api/commands/test/', data, content_type='application/json')
    
    print(f"Status: {response.status_code}")
    if response.status_code == 200:
        result = response.json()
        print(f"Succès: {result.get('success', False)}")
        if result.get('success'):
            execution_result = result.get('data', {}).get('execution_result', {})
            if execution_result.get('success'):
                grid = execution_result.get('grid')
                print("Grille résultante:")
                for row in grid:
                    print(row)
                return True
            else:
                print(f"Erreur d'exécution: {execution_result.get('error')}")
        else:
            print(f"Erreur API: {result.get('error')}")
    else:
        print(f"Erreur HTTP: {response.content}")
    
    return False


def test_error_cases_api():
    """Test des cas d'erreur via l'API"""
    print("\n🧪 Test API cas d'erreur")
    
    client = Client()
    task = create_test_task()
    
    # Test avec facteur invalide
    commands = [
        "INIT 2x2",
        "EDIT 1 [0,0]",
        "MULTIPLIER 1 [0,0 1,1]"  # Facteur invalide
    ]
    
    data = {
        'task_id': task.task_id,
        'commands': '\n'.join(commands)
    }
    
    response = client.post('/api/commands/test/', data, content_type='application/json')
    
    print(f"Status: {response.status_code}")
    if response.status_code == 200:
        result = response.json()
        execution_result = result.get('data', {}).get('execution_result', {})
        success = execution_result.get('success', True)
        error = execution_result.get('error', '')
        
        print(f"Succès (attendu False): {success}")
        print(f"Erreur: {error}")
        
        # Le test réussit si la commande échoue avec une erreur appropriée
        return not success and 'facteur' in error.lower()
    
    return False


def main():
    """Fonction principale de test"""
    print("=== Test API des commandes MULTIPLIER et DIVISER ===")
    
    tests = [
        ("API MULTIPLIER", test_multiplier_api),
        ("API DIVISER", test_diviser_api),
        ("API Cas d'erreur", test_error_cases_api)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
            print(f"\n✅ Test {test_name}: {'RÉUSSI' if result else 'ÉCHOUÉ'}")
        except Exception as e:
            results.append((test_name, False))
            print(f"\n❌ Test {test_name}: ERREUR - {e}")
            import traceback
            traceback.print_exc()
    
    print("\n=== Résumé des tests API ===")
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ RÉUSSI" if result else "❌ ÉCHOUÉ"
        print(f"{test_name}: {status}")
    
    print(f"\nRésultat global: {passed}/{total} tests réussis")
    
    if passed == total:
        print("🎉 Tous les tests API sont passés !")
        return True
    else:
        print("⚠️ Certains tests API ont échoué")
        return False


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
