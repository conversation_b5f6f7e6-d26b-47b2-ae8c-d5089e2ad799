#!/usr/bin/env python
"""
Test du scénario complet avec MULTIPLY
TRANSFERT {INIT 3x3; EDIT 7 [0,0]; EDIT 7 [0,2]; EDIT 7 [1,0]; EDIT 7 [1,2]; EDIT 7 [2,0]; EDIT 7 [2,1]}
RESIZE 9x9
COPY (COLOR 7 [0,0 2,2])
MULTIPLY 3 [0,0 8,8]
END
"""

import os
import sys
import django
import numpy as np

# Configuration Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from command_system.command_executor import CommandExecutor
from command_system.unified_command import UnifiedCommand


def test_complete_scenario():
    """Test du scénario complet"""
    print("🧪 Test du scénario complet MULTIPLY")
    
    executor = CommandExecutor()
    
    # Séquence de commandes
    commands = [
        "INIT 3x3",
        "EDIT 7 [0,0]",
        "EDIT 7 [0,2]", 
        "EDIT 7 [1,0]",
        "EDIT 7 [1,2]",
        "EDIT 7 [2,0]",
        "EDIT 7 [2,1]",
        "RESIZE 9x9",
        "COPY (COLOR 7 [0,0 2,2])",
        "MULTIPLY 3 [0,0 8,8]"
    ]
    
    print("Exécution des commandes:")
    for i, command_str in enumerate(commands):
        print(f"{i+1}. {command_str}")
        
        cmd = UnifiedCommand.parse(command_str)
        if not cmd:
            print(f"❌ Erreur de parsing: {command_str}")
            return False
            
        success = executor._execute_unified_command(cmd)
        if not success:
            print(f"❌ Erreur d'exécution: {executor.error}")
            return False
        
        # Afficher la grille après certaines étapes clés
        if command_str.startswith("EDIT 7 [2,1]"):
            print("\nGrille après création du motif initial:")
            print(executor.grid)
        elif command_str.startswith("RESIZE"):
            print(f"\nGrille après RESIZE {executor.height}x{executor.width}:")
            print(executor.grid)
        elif command_str.startswith("COPY"):
            print(f"\nPresse-papier après COPY:")
            if executor.clipboard is not None:
                print("Contenu:")
                print(executor.clipboard)
                if executor.clipboard_mask is not None:
                    print("Masque:")
                    print(executor.clipboard_mask.astype(int))
        elif command_str.startswith("MULTIPLY"):
            print(f"\nGrille finale après MULTIPLY:")
            print(executor.grid)
    
    # Vérifier le résultat attendu
    expected_pattern = np.array([
        [7, 0, 7, 7, 0, 7, 7, 0, 7],
        [7, 0, 7, 7, 0, 7, 7, 0, 7],
        [7, 7, 0, 7, 7, 0, 7, 7, 0],
        [7, 0, 7, 7, 0, 7, 7, 0, 7],
        [7, 0, 7, 7, 0, 7, 7, 0, 7],
        [7, 7, 0, 7, 7, 0, 7, 7, 0],
        [7, 0, 7, 7, 0, 7, 7, 0, 7],
        [7, 0, 7, 7, 0, 7, 7, 0, 7],
        [7, 7, 0, 7, 7, 0, 7, 7, 0]
    ])
    
    matches = np.array_equal(executor.grid, expected_pattern)
    print(f"\n✅ Résultat conforme à l'attendu: {matches}")
    
    if not matches:
        print("\nAttendu:")
        print(expected_pattern)
        print("\nObtenu:")
        print(executor.grid)
        
        # Compter les différences
        diff = (executor.grid != expected_pattern)
        diff_count = np.sum(diff)
        print(f"\nNombre de cellules différentes: {diff_count}")
    
    return matches


def test_validation_scenario():
    """Test de validation du scénario"""
    print("\n🧪 Test de validation du scénario")
    
    from ai.command_decompressor import CommandDecompressor
    
    scenario = """TRANSFERT {INIT 3x3; EDIT 7 [0,0]; EDIT 7 [0,2]; EDIT 7 [1,0]; EDIT 7 [1,2]; EDIT 7 [2,0]; EDIT 7 [2,1]}
RESIZE 9x9
COPY (COLOR 7 [0,0 2,2])
MULTIPLY 3 [0,0 8,8]
END"""
    
    decompressor = CommandDecompressor()
    
    try:
        # Décomposer le scénario
        commands = scenario.strip().split('\n')
        decompressed = decompressor.decompress_commands(commands)
        
        print(f"✅ Décompression réussie: {len(decompressed)} commandes")
        
        # Vérifier que MULTIPLY est bien validé
        multiply_found = False
        for cmd in decompressed:
            if 'MULTIPLY' in cmd:
                multiply_found = True
                print(f"✅ Commande MULTIPLY trouvée: {cmd}")
                
                # Tester la validation individuelle
                is_valid = decompressor._is_valid_individual_command(cmd)
                print(f"✅ Validation individuelle: {is_valid}")
                break
        
        if not multiply_found:
            print("❌ Commande MULTIPLY non trouvée dans la décompression")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ Erreur de validation: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Fonction principale"""
    print("=== Test du scénario complet MULTIPLY ===")
    
    tests = [
        ("Scénario complet", test_complete_scenario),
        ("Validation scénario", test_validation_scenario)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
            print(f"\n✅ Test {test_name}: {'RÉUSSI' if result else 'ÉCHOUÉ'}")
        except Exception as e:
            results.append((test_name, False))
            print(f"\n❌ Test {test_name}: ERREUR - {e}")
            import traceback
            traceback.print_exc()
    
    print("\n=== Résumé des tests ===")
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ RÉUSSI" if result else "❌ ÉCHOUÉ"
        print(f"{test_name}: {status}")
    
    print(f"\nRésultat global: {passed}/{total} tests réussis")
    
    if passed == total:
        print("🎉 Tous les tests sont passés !")
        return True
    else:
        print("⚠️ Certains tests ont échoué")
        return False


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
