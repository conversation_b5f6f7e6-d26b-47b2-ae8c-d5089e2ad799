// src/components/resolution/utils/selectionTransformUtils.ts

export type SelectionTransformationType = 'rotateRight' | 'rotateLeft' | 'flipHorizontal' | 'flipVertical' | 'multiply' | 'divide';

interface RelativeCoord {
  row: number;
  col: number;
}

/**
 * Transforme des coordonnées relatives en fonction d'un type de transformation,
 * de la hauteur et de la largeur de la zone de sélection.
 *
 * @param relCoord - Les coordonnées relatives { row, col } par rapport au coin supérieur gauche de la sélection.
 * @param transformType - Le type de transformation à appliquer.
 * @param selectionHeight - La hauteur de la zone de sélection.
 * @param selectionWidth - La largeur de la zone de sélection.
 * @returns Les nouvelles coordonnées relatives { row, col } après transformation.
 */
export const transformRelativeCoord = (
  relCoord: RelativeCoord,
  transformType: SelectionTransformationType,
  selectionHeight: number,
  selectionWidth: number,
  factor?: number
): RelativeCoord => {
  let newRelRow: number;
  let newRelCol: number;

  switch (transformType) {
    case 'rotateRight':
      // Rotation à droite (90° sens horaire)
      // (x, y) -> (y, H-1-x) si l'origine est en haut à gauche et x est la colonne, y la ligne.
      // Ici, relCol est x, relRow est y. H est selectionHeight.
      // newRelRow (nouvelle ligne) = relCol (ancienne colonne)
      // newRelCol (nouvelle colonne) = selectionHeight - 1 - relRow (ancienne ligne)
      newRelRow = relCoord.col;
      newRelCol = selectionHeight - 1 - relCoord.row;
      break;
    case 'rotateLeft':
      // Rotation à gauche (90° sens antihoraire)
      // (x, y) -> (W-1-y, x) si l'origine est en haut à gauche et x est la colonne, y la ligne.
      // Ici, relCol est x, relRow est y. W est selectionWidth.
      // newRelRow (nouvelle ligne) = selectionWidth - 1 - relCoord.col (ancienne colonne)
      // newRelCol (nouvelle colonne) = relCoord.row (ancienne ligne)
      newRelRow = selectionWidth - 1 - relCoord.col;
      newRelCol = relCoord.row;
      break;
    case 'flipHorizontal':
      // Symétrie horizontale
      // (x, y) -> (W-1-x, y)
      // newRelRow (nouvelle ligne) = relCoord.row (ancienne ligne)
      // newRelCol (nouvelle colonne) = selectionWidth - 1 - relCoord.col (ancienne colonne)
      newRelRow = relCoord.row;
      newRelCol = selectionWidth - 1 - relCoord.col;
      break;
    case 'flipVertical':
      // Symétrie verticale
      // (x, y) -> (x, H-1-y)
      // newRelRow (nouvelle ligne) = selectionHeight - 1 - relCoord.row (ancienne ligne)
      // newRelCol (nouvelle colonne) = relCoord.col (ancienne colonne)
      newRelRow = selectionHeight - 1 - relCoord.row;
      newRelCol = relCoord.col;
      break;
    case 'multiply':
      // Multiplication : chaque cellule est répliquée factor x factor fois
      if (!factor || factor < 2) {
        newRelRow = relCoord.row;
        newRelCol = relCoord.col;
      } else {
        newRelRow = relCoord.row * factor;
        newRelCol = relCoord.col * factor;
      }
      break;
    case 'divide':
      // Division : échantillonnage d'une cellule sur factor
      if (!factor || factor < 2) {
        newRelRow = relCoord.row;
        newRelCol = relCoord.col;
      } else {
        newRelRow = Math.floor(relCoord.row / factor);
        newRelCol = Math.floor(relCoord.col / factor);
      }
      break;
    default:
      // Par défaut, ne pas transformer (ne devrait pas arriver avec des types stricts)
      newRelRow = relCoord.row;
      newRelCol = relCoord.col;
  }

  return { row: newRelRow, col: newRelCol };
};