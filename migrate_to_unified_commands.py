#!/usr/bin/env python3
"""
Script de migration pour convertir toutes les commandes legacy vers le format unifié

USAGE:
    python migrate_to_unified_commands.py                    # Migration complète
    python migrate_to_unified_commands.py --dry-run          # Simulation seulement
    python migrate_to_unified_commands.py --task-id 00d62c1b # Tâche spécifique
    python migrate_to_unified_commands.py --subset training  # Subset spécifique
    python migrate_to_unified_commands.py --skip-code        # Seulement les .agi

Le script migre :
- Les fichiers TypeScript/React dans frontend/src/components/ 
- Les fichiers .agi dans arcdata/ (avec backups automatiques)

COMMANDES SUPPORTÉES:
  Base:           EDIT, FILL, CLEAR, SURROUND, REPLACE, SELECT
  Groupement:     TRANSFERT et MOTIF
  Structurelles:  INSERT ROWS/COLUMNS, DELETE ROWS/COLUMNS, EXTRACT
  Transformations: FLIP HORIZONTAL/VERTICAL, ROTATE LEFT/RIGHT, MULTIPLY, DIVIDE
  Presse-papier:  COPY, CUT, PASTE
  Sélections:     INVERT, COLOR
  Système:        RESIZE, END

FORMATS DE CONVERSION:
  EDIT 0,0 7           → EDIT 7 [0,0]
  FILL 5 0,0 1,1       → FILL 5 [0,0 1,1]
  REPLACE 1,8 5 0,0 1,1 → REPLACE 1,8 5 [0,0 1,1]
  FLIP HORIZONTAL 0,0 1,1 → FLIP HORIZONTAL ([0,0 1,1])
  TRANSFERT (commands) → TRANSFERT {commands}

Options:
  --dry-run      Simule la migration sans modifier les fichiers
  --task-id ID   Migre seulement la tâche spécifiée
  --subset NAME  Migre seulement le subset (training/evaluation)
  --skip-code    Ignore les fichiers TypeScript/React
  --skip-agi     Ignore les fichiers .agi
  --max-files N  Limite le nombre de fichiers traités
"""

import os
import re
import glob

def migrate_file(file_path, dry_run=False):
    """Migre un fichier vers les commandes unifiées"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
          # Déterminer le type de fichier
        if file_path.endswith('.agi'):
            return migrate_agi_file(file_path, content, original_content, dry_run)
        else:
            return migrate_code_file(file_path, content, original_content, dry_run)
            
    except Exception as e:
        print(f"❌ Erreur migration {file_path}: {e}")
        return False

def migrate_agi_file(file_path, content, original_content, dry_run=False):
    """Migre un fichier .agi vers les commandes unifiées"""    # Vérifier si le fichier a encore des commandes legacy à migrer
    legacy_patterns = [
        r'\bINVERT_SELECTION\b',
        r'\bSELECTS\s*\(',
        r'\bFLOODFILLS\s*\(',  # FLOODFILLS {FLOODFILL ...}
        r'\bEDITS\s*\(',  # EDITS {EDIT ...}
        r'\bSELECT\s+RELEASE\b',
        r'\bSELECT\s+COLOR\s+\d+',
        r'\bEDIT\s+\d+,\d+\s+\d+',  # EDIT x,y value
        r'\bFILL\s+\d+\s+\d+,\d+\s+\d+,\d+',  # FILL value x,y x2,y2
        r'\(\(SELECT_COLOR[^)]+\)\)',  # Doubles parenthèses dans SELECT_COLOR
        # Détecter EDITS décompressés (plusieurs EDIT consécutifs qui pourraient être groupés)
        r'EDIT\s+\d+\s+\[[^\]]+\]\s*\n\s*EDIT\s+\d+\s+\[[^\]]+\]\s*\n\s*EDIT\s+\d+\s+\[[^\]]+\]',
    ]
    
    has_legacy = any(re.search(pattern, content) for pattern in legacy_patterns)
    
    if not has_legacy and '[' in content and ']' in content:
        print(f"✅ Déjà unifié: {file_path}")
        return False
    
    # === 1. COMMANDES DE BASE ===
    
    # EDIT 0,0 7 -> EDIT 7 [0,0]
    content = re.sub(
        r'\bEDIT\s+(\d+),(\d+)\s+(\d+)',
        r'EDIT \3 [\1,\2]',
        content
    )
      # SELECT 0,0 1,1 -> SELECT [0,0 1,1]  
    content = re.sub(
        r'\bSELECT\s+(\d+),(\d+)\s+(\d+),(\d+)',
        r'SELECT [\1,\2 \3,\4]',
        content
    )      # SELECTS(SELECT ...; SELECT ...) -> Analyse et conversion intelligente
    # Migration complète de SELECTS vers les commandes appropriées
    def convert_selects_group(match):
        selects_content = match.group(1)
        # Découper par ; et traiter chaque partie
        parts = [part.strip() for part in selects_content.split(';') if part.strip()]
        
        converted_commands = []
        i = 0
        
        while i < len(parts):
            part = parts[i].strip()
            
            # Cas spécial: SELECT RELEASE -> SELECT_RELEASE
            if part == 'SELECT RELEASE':
                converted_commands.append('SELECT_RELEASE')
                i += 1
                continue
            
            # Cas spécial: SELECT COLOR x y,z w,t -> (SELECT_COLOR x [y,z w,t])
            color_match = re.match(r'SELECT\s+COLOR\s+(\d+(?:,\d+)*)\s+(.+)', part)
            if color_match:
                colors = color_match.group(1)
                coords_part = color_match.group(2).strip()
                
                # Convertir les coordonnées au format unifié
                if re.match(r'\[\d+,\d+\s+\d+,\d+\]', coords_part):
                    # Déjà au bon format: [0,0 4,3]
                    coords_formatted = coords_part
                elif re.match(r'\d+,\d+\s+\d+,\d+', coords_part):
                    # Format legacy: 0,0 4,3 -> [0,0 4,3]
                    coords_formatted = f'[{coords_part}]'
                else:
                    coords_formatted = f'[{coords_part}]'
                
                converted_commands.append(f'(SELECT_COLOR {colors} {coords_formatted})')
                i += 1
                continue
            
            # Cas normal: SELECT avec coordonnées
            coord_match = re.match(r'SELECT\s+(.+)', part)
            if coord_match:
                coords = coord_match.group(1).strip()
                
                # Formater les coordonnées
                if re.match(r'\[\d+,\d+\s+\d+,\d+\]', coords):
                    formatted_coords = coords
                elif re.match(r'\d+,\d+\s+\d+,\d+', coords):
                    formatted_coords = f'[{coords}]'
                else:
                    formatted_coords = f'[{coords}]'
                
                # Vérifier s'il y a un SELECT COLOR suivant avec les mêmes coordonnées
                found_matching_color = False
                if i + 1 < len(parts):
                    next_part = parts[i + 1].strip()
                    next_color_match = re.match(r'SELECT\s+COLOR\s+(\d+(?:,\d+)*)\s+(.+)', next_part)
                    if next_color_match:
                        next_colors = next_color_match.group(1)
                        next_coords_part = next_color_match.group(2).strip()
                        
                        # Formater les coordonnées du SELECT COLOR
                        if re.match(r'\[\d+,\d+\s+\d+,\d+\]', next_coords_part):
                            next_coords_formatted = next_coords_part
                        elif re.match(r'\d+,\d+\s+\d+,\d+', next_coords_part):
                            next_coords_formatted = f'[{next_coords_part}]'
                        else:
                            next_coords_formatted = f'[{next_coords_part}]'
                        
                        # Si les coordonnées correspondent, fusionner et ignorer le SELECT
                        if formatted_coords == next_coords_formatted:
                            converted_commands.append(f'(SELECT_COLOR {next_colors} {formatted_coords})')
                            found_matching_color = True
                            i += 2  # Passer les deux commandes
                            continue
                
                # Pas de fusion possible, ajouter le SELECT normal
                if not found_matching_color:
                    converted_commands.append(f'SELECT {formatted_coords}')
                    i += 1
                continue
            
            # Si ce n'est pas reconnu, l'ajouter tel quel
            converted_commands.append(part)
            i += 1
          # Joindre toutes les commandes converties
        return '\n'.join(converted_commands)
    
    content = re.sub(
        r'\bSELECTS\s*\(([^)]+)\)',
        convert_selects_group,
        content    )
    
    # SELECT COLOR x coords -> (SELECT_COLOR x [coords]) pour les cas isolés
    content = re.sub(
        r'\bSELECT\s+COLOR\s+([\d,]+)\s+(\d+),(\d+)\s+(\d+),(\d+)',
        r'(SELECT_COLOR \1 [\2,\3 \4,\5])',
        content
    )
      # SELECT RELEASE isolé -> SELECT_RELEASE
    content = re.sub(
        r'\bSELECT\s+RELEASE\b',
        'SELECT_RELEASE',
        content
    )
    
    # Conversion des commandes INVERT_SELECTION legacy
    # INVERT_SELECTION isolé -> SELECT_INVERT (nécessite une sélection active)
    content = re.sub(
        r'\bINVERT_SELECTION\b',
        'SELECT_INVERT',
        content
    )
    
    # Corriger les fusions incorrectes comme INVERT_SELECTIONREPLACE
    # INVERT_SELECTIONREPLACE params -> SELECT_INVERT + REPLACE params
    content = re.sub(
        r'\bINVERT_SELECTION([A-Z]+)\s+([^\n]+)',
        lambda m: f'SELECT_INVERT\n{m.group(1)} {m.group(2)}',
        content
    )
      # === FLOODFILLS -> FLOODFILL individuels ===
    # FLOODFILLS {FLOODFILL x,y color; FLOODFILL x2,y2 color2} -> FLOODFILL color [x,y]; FLOODFILL color2 [x2,y2]
    def convert_floodfills_group(match):
        floodfills_content = match.group(1)
        # Découper par ; et traiter chaque partie
        parts = [part.strip() for part in floodfills_content.split(';') if part.strip()]
        
        converted_commands = []
        for part in parts:
            part = part.strip()
            # Matcher format legacy: FLOODFILL x,y color
            floodfill_legacy_match = re.match(r'FLOODFILL\s+(\d+),(\d+)\s+(\d+)', part)
            if floodfill_legacy_match:
                x, y, color = floodfill_legacy_match.groups()
                converted_commands.append(f'FLOODFILL {color} [{x},{y}]')
                continue
            
            # Matcher format moderne mais dans groupe: FLOODFILL color [x,y]
            floodfill_modern_match = re.match(r'FLOODFILL\s+(\d+)\s+\[(\d+),(\d+)\]', part)
            if floodfill_modern_match:
                color, x, y = floodfill_modern_match.groups()
                converted_commands.append(f'FLOODFILL {color} [{x},{y}]')
                continue
            
            # Si le format n'est pas reconnu, garder tel quel
            converted_commands.append(part)
        
        # Joindre toutes les commandes converties
        return '\n'.join(converted_commands)
    
    content = re.sub(
        r'\bFLOODFILLS\s*\(\s*([^)]+)\)',
        convert_floodfills_group,
        content
    )    # === EDITS -> Garder le format groupé ===
    # EDITS {EDIT color [x,y]; EDIT color2 [x2,y2]} -> EDITS {EDIT color [x,y]; EDIT color2 [x2,y2]}
    # Ne pas décompresser, juste normaliser le format interne si nécessaire
    def normalize_edits_group(match):
        edits_content = match.group(1)
        # Garder le groupe intact, juste nettoyer les espaces
        edits_content = re.sub(r'\s+', ' ', edits_content.strip())
        return f'EDITS {{edits_content}}'
    
    content = re.sub(
        r'\bEDITS\s*\(\s*([^)]+)\)',
        normalize_edits_group,
        content
    )

    # SELECT COLOR 1,2,3 0,0 1,1 -> (SELECT_COLOR 1,2,3 [0,0 1,1])
    content = re.sub(
        r'\bSELECT_COLOR\s+([\d,]+)\s+(\d+),(\d+)\s+(\d+),(\d+)',
        r'(SELECT_COLOR \1 [\2,\3 \4,\5])',
        content
    )
    
    # === 2. MODIFICATIONS STRUCTURELLES ===
    
    # INSERT 5 ROWS BELOW 0,0 1,1 -> INSERT 5 ROWS BELOW [0,0 1,1]
    content = re.sub(
        r'\bINSERT\s+(\d+)\s+(ROWS|COLUMNS)\s+(ABOVE|BELOW|BEFORE|AFTER)\s+(\d+),(\d+)\s+(\d+),(\d+)',
        r'INSERT \1 \2 \3 [\4,\5 \6,\7]',
        content
    )
    
    # DELETE ROWS 0,0 1,1 -> DELETE ROWS [0,0 1,1]
    content = re.sub(
        r'\bDELETE\s+(ROWS|COLUMNS)\s+(\d+),(\d+)\s+(\d+),(\d+)',
        r'DELETE \1 [\2,\3 \4,\5]',
        content
    )
    
    # EXTRACT 4,4 6,6 -> EXTRACT [4,4 6,6]
    content = re.sub(
        r'\bEXTRACT\s+(\d+),(\d+)\s+(\d+),(\d+)',
        r'EXTRACT [\1,\2 \3,\4]',
        content
    )
    
    # === 3. TRANSFORMATIONS (MOTIFS avec parenthèses) ===
    
    # FLIP HORIZONTAL 0,0 1,1 -> FLIP HORIZONTAL ([0,0 1,1])
    content = re.sub(
        r'\bFLIP\s+(HORIZONTAL|VERTICAL)\s+(\d+),(\d+)\s+(\d+),(\d+)',
        r'FLIP \1 ([\2,\3 \4,\5])',
        content
    )
    
    # ROTATE LEFT 0,0 1,1 -> ROTATE LEFT ([0,0 1,1])
    content = re.sub(
        r'\bROTATE\s+(LEFT|RIGHT)\s+(\d+),(\d+)\s+(\d+),(\d+)',
        r'ROTATE \1 ([\2,\3 \4,\5])',
        content
    )
    
    # === 4. COMMANDES PRESSE-PAPIER (MOTIFS avec parenthèses) ===
    
    # COPY 0,0 1,1 -> COPY ([0,0 1,1])
    content = re.sub(
        r'\bCOPY\s+(\d+),(\d+)\s+(\d+),(\d+)',
        r'COPY ([\1,\2 \3,\4])',
        content
    )
    
    # CUT 0,0 1,1 -> CUT ([0,0 1,1])
    content = re.sub(
        r'\bCUT\s+(\d+),(\d+)\s+(\d+),(\d+)',
        r'CUT ([\1,\2 \3,\4])',
        content
    )
    
    # PASTE 0,0 -> PASTE ([0,0])
    content = re.sub(
        r'\bPASTE\s+(\d+),(\d+)',
        r'PASTE ([\1,\2])',
        content
    )
    
    # === 5. SÉLECTIONS SPÉCIALES (avec parenthèses) ===
    
    # SELECT_INVERT 0,0 1,1 -> SELECT_INVERT ([0,0 1,1])
    content = re.sub(
        r'\bSELECT_INVERT\s+(\d+),(\d+)\s+(\d+),(\d+)',
        r'SELECT_INVERT ([\1,\2 \3,\4])',
        content
    )
    
    # SELECT_COLOR 1,2,3 0,0 1,1 -> (SELECT_COLOR 1,2,3 [0,0 1,1])
    content = re.sub(
        r'\bSELECT_COLOR\s+([\d,]+)\s+(\d+),(\d+)\s+(\d+),(\d+)',
        r'(SELECT_COLOR \1 [\2,\3 \4,\5])',
        content
    )
    
    # === 6. TRANSFERT (accolades { }) ===
    
    # TRANSFERT (commandes) -> TRANSFERT {commandes}
    content = re.sub(
        r'\bTRANSFERT\s*\(([^)]+)\)',
        r'TRANSFERT {\1}',
        content
    )
    
    # Mise à jour du format interne TRANSFERT pour EDIT
    # EDIT 0,0 7 dans TRANSFERT -> EDIT 7 [0,0]
    def update_transfert_commands(match):
        transfert_content = match.group(1)
        # Appliquer les mêmes transformations dans TRANSFERT
        transfert_content = re.sub(r'\bEDIT\s+(\d+),(\d+)\s+(\d+)', r'EDIT \3 [\1,\2]', transfert_content)
        transfert_content = re.sub(r'\bFILL\s+(\d+)\s+(\d+),(\d+)\s+(\d+),(\d+)', r'FILL \1 [\2,\3 \4,\5]', transfert_content)
        return f'TRANSFERT {{{transfert_content}}}'
    
    content = re.sub(r'TRANSFERT\s*\{([^}]+)\}', update_transfert_commands, content)
    
    # === OPTIMISATIONS POST-CONVERSION ===
      # Optimiser (SELECT_COLOR ...) suivi d'actions compatibles
    # (SELECT_COLOR x [coords])\nCOPY ([...]) -> COPY (SELECT_COLOR x [coords])
    def optimize_select_color_integration(match):
        select_color_line = match.group(1).strip()
        action_line = match.group(2).strip()
        
        # Extraire l'action et ses paramètres
        action_match = re.match(r'(\w+)\s*\(([^)]+)\)', action_line)
        if action_match:
            action = action_match.group(1)
            params = action_match.group(2)
            # Les actions compatibles avec SELECT_COLOR
            if action in ['COPY', 'CUT', 'PASTE', 'FILL', 'CLEAR', 'EDIT']:
                # UNE SEULE paire de parenthèses pour SELECT_COLOR
                return f'{action} ({select_color_line})'
        
        # Si pas compatible, retourner tel quel
        return f'{select_color_line}\n{action_line}'
    
    content = re.sub(
        r'\((SELECT_COLOR[^)]+)\)\s*\n\s*(\w+\s*\([^)]+\))',
        optimize_select_color_integration,
        content,
        flags=re.MULTILINE
    )
    
    # Sauvegarder si modifié
    if content != original_content:
        if not dry_run:
            # Créer backup
            backup_path = file_path + '.backup'
            with open(backup_path, 'w', encoding='utf-8') as f:
                f.write(original_content)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ Migré .agi: {file_path} (backup: {backup_path})")
        else:
            print(f"🔍 SERAIT MIGRÉ .agi: {file_path}")
        return True
    else:
        print(f"ℹ️  Pas de changement .agi: {file_path}")
        return False

def migrate_code_file(file_path, content, original_content, dry_run=False):
    """Migre un fichier de code TypeScript/React"""
    
    # === COMMANDES DE BASE ===
    
    # 1. EDIT 0,0 7 -> EDIT 7 [0,0]
    content = re.sub(
        r"addCommand\('EDIT',\s*`([^`]+)`\)",
        lambda m: convert_edit_command(m.group(1)),
        content
    )
      # 2. SELECT 0,0 1,1 -> SELECT [0,0 1,1]
    content = re.sub(
        r"addCommand\('SELECT',\s*`([^`]+)`\)",
        lambda m: convert_select_command(m.group(1)),
        content
    )
    
    # 2b. SELECTS(SELECT ...; SELECT ...) -> SELECT ([...] +[...])
    content = re.sub(
        r"addCommand\('SELECTS',\s*`([^`]+)`\)",
        lambda m: convert_selects_command(m.group(1)),
        content
    )
    
    # 3. FILL 5 0,0 1,1 -> FILL 5 [0,0 1,1]
    content = re.sub(
        r"addCommand\('FILL',\s*`([^`]+)`\)",
        lambda m: convert_fill_command(m.group(1)),
        content
    )
    
    # 4. CLEAR 0,0 1,1 -> CLEAR [0,0 1,1]
    content = re.sub(
        r"addCommand\('CLEAR',\s*`([^`]+)`\)",
        lambda m: convert_clear_command(m.group(1)),
        content
    )
    
    # 5. SURROUND 3 0,0 1,1 -> SURROUND 3 [0,0 1,1]
    content = re.sub(
        r"addCommand\('SURROUND',\s*`([^`]+)`\)",
        lambda m: convert_surround_command(m.group(1)),
        content
    )
    
    # 6. REPLACE 1,8,3 5 0,0 1,1 -> REPLACE 1,8,3 5 [0,0 1,1]
    content = re.sub(
        r"addCommand\('REPLACE',\s*`([^`]+)`\)",
        lambda m: convert_replace_command(m.group(1)),
        content
    )
    
    # === COMMANDES PRESSE-PAPIER ===
    
    # 7. COPY 0,0 1,1 -> COPY ([0,0 1,1])
    content = re.sub(
        r"addCommand\('COPY',\s*`([^`]+)`\)",
        lambda m: convert_copy_command(m.group(1)),
        content
    )
    
    # 8. CUT 0,0 1,1 -> CUT ([0,0 1,1])
    content = re.sub(
        r"addCommand\('CUT',\s*`([^`]+)`\)",
        lambda m: convert_cut_command(m.group(1)),
        content
    )
    
    # 9. PASTE 0,0 -> PASTE ([0,0])
    content = re.sub(
        r"addCommand\('PASTE',\s*`([^`]+)`\)",
        lambda m: convert_paste_command(m.group(1)),
        content
    )
    
    # === TRANSFORMATIONS ===
    
    # 10. FLIP HORIZONTAL/VERTICAL 0,0 1,1 -> FLIP HORIZONTAL ([0,0 1,1])
    content = re.sub(
        r"addCommand\('FLIP\s+(HORIZONTAL|VERTICAL)',\s*`([^`]+)`\)",
        lambda m: convert_flip_command(m.group(1), m.group(2)),
        content
    )
    
    # 11. ROTATE LEFT/RIGHT 0,0 1,1 -> ROTATE LEFT ([0,0 1,1])
    content = re.sub(
        r"addCommand\('ROTATE\s+(LEFT|RIGHT)',\s*`([^`]+)`\)",
        lambda m: convert_rotate_command(m.group(1), m.group(2)),
        content
    )
    
    # === COMMANDES STRUCTURELLES ===
    
    # 12. INSERT/DELETE ROWS/COLUMNS
    content = re.sub(
        r"addCommand\('(INSERT|DELETE)\s+(ROWS|COLUMNS)(?:\s+(ABOVE|BELOW|BEFORE|AFTER))?',\s*`([^`]+)`\)",
        lambda m: convert_structure_command(m.group(1), m.group(2), m.group(3), m.group(4)),
        content
    )
    
    # 13. EXTRACT 4,4 6,6 -> EXTRACT [4,4 6,6]
    content = re.sub(
        r"addCommand\('EXTRACT',\s*`([^`]+)`\)",
        lambda m: convert_extract_command(m.group(1)),
        content
    )
    
    # === SÉLECTIONS SPÉCIALES ===
    
    # 14. SELECT_INVERT 0,0 1,1 -> SELECT_INVERT ([0,0 1,1])
    content = re.sub(
        r"addCommand\('SELECT_INVERT',\s*`([^`]+)`\)",
        lambda m: convert_select_invert_command(m.group(1)),
        content
    )
    
    # 15. SELECT_COLOR 1,2,3 0,0 1,1 -> (SELECT_COLOR 1,2,3 [0,0 1,1])
    content = re.sub(
        r"addCommand\('SELECT_COLOR',\s*`([^`]+)`\)",
        lambda m: convert_select_color_command(m.group(1)),
        content
    )
    
    # === COMMANDES SYSTÈME ===
    
    # 16. Remplacer les commandes simples (END, RESIZE, etc.)
    content = re.sub(
        r"addCommand\('(END|RESIZE)',\s*`?([^`]*)`?\)",
        lambda m: convert_simple_command(m.group(1), m.group(2)),
        content
    )
    
    # 17. TRANSFERT (commandes) -> TRANSFERT {commandes}
    content = re.sub(
        r"addCommand\('TRANSFERT',\s*`([^`]+)`\)",
        lambda m: convert_transfert_command(m.group(1)),
        content
    )
      # 18. Remplacer addCommand sans paramètres
    content = re.sub(
        r"addCommand\('([^']+)'\)",
        lambda m: f"addUnifiedCommand('{m.group(1)}')",
        content
    )
    
    # Sauvegarder si modifié
    if content != original_content:
        if not dry_run:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ Migré: {file_path}")
        else:
            print(f"🔍 SERAIT MIGRÉ: {file_path}")
        return True
    else:
        print(f"ℹ️  Pas de changement: {file_path}")
        return False

def convert_edit_command(params):
    """Convertit une commande EDIT legacy vers format unifié"""
    # Exemple: "0,0 7" -> "EDIT 7 [0,0]"
    parts = params.strip().split()
    if len(parts) >= 2:
        coords = parts[0]  # "0,0"
        color = parts[1]   # "7"
        return f"addUnifiedCommand('EDIT', '{color}', ['{coords}'])"
    return f"addUnifiedCommand('EDIT', '', ['{params}'])"

def convert_select_command(params):
    """Convertit une commande SELECT legacy vers format unifié"""
    # Exemple: "+0,0 1,1" -> "SELECT [+0,0 1,1]"
    # Exemple: "0,0 1,1" -> "SELECT [0,0 1,1]"
    coords = params.strip().split()
    if len(coords) >= 2:
        coord_str = ' '.join(coords)
        return f"addUnifiedCommand('SELECT', undefined, ['{coord_str}'])"
    return f"addUnifiedCommand('SELECT', undefined, ['{params}'])"

def convert_selects_command(params):
    """Convertit une commande SELECTS legacy vers les commandes appropriées"""
    # Exemple: "SELECT RELEASE; SELECT 0,0 1,1; SELECT COLOR 1 0,0 1,1" 
    # -> Séparer en commandes individuelles appropriées
    
    # Découper par ; et traiter chaque partie
    parts = [part.strip() for part in params.split(';') if part.strip()]
    
    converted_commands = []
    coordinate_selects = []  # Pour accumuler les vrais SELECT avec coordonnées
    
    for part in parts:
        part = part.strip()
        
        # Cas spécial: SELECT RELEASE -> SELECT_RELEASE
        if part == 'SELECT RELEASE':
            # Si on a des sélections en attente, les convertir d'abord
            if coordinate_selects:
                converted_commands.append(convert_ts_coordinate_selects(coordinate_selects))
                coordinate_selects = []
            converted_commands.append("addUnifiedCommand('SELECT_RELEASE')")
            continue
        
        # Cas spécial: SELECT COLOR x y,z w,t -> (SELECT_COLOR x [y,z w,t])
        import re
        color_match = re.match(r'SELECT\s+COLOR\s+(\d+(?:,\d+)*)\s+(.+)', part)
        if color_match:
            # Si on a des sélections en attente, les convertir d'abord
            if coordinate_selects:
                converted_commands.append(convert_ts_coordinate_selects(coordinate_selects))
                coordinate_selects = []
            
            colors = color_match.group(1)
            coords_part = color_match.group(2).strip()
            
            # Convertir les coordonnées au format unifié
            if re.match(r'\[\d+,\d+\s+\d+,\d+\]', coords_part):
                coords_formatted = coords_part
            elif re.match(r'\d+,\d+\s+\d+,\d+', coords_part):
                coords_formatted = f'[{coords_part}]'
            else:
                coords_formatted = f'[{coords_part}]'
            
            converted_commands.append(f"addUnifiedCommand('SELECT_COLOR', '({colors})', ['{coords_formatted}'])")
            continue
        
        # Cas normal: SELECT avec coordonnées -> à accumuler
        coord_match = re.match(r'SELECT\s+(.+)', part)
        if coord_match:
            coordinate_selects.append(coord_match.group(1).strip())
            continue
        
        # Si ce n'est pas reconnu, l'ajouter tel quel (fallback)
        converted_commands.append(f"addUnifiedCommand('{part}')")
    
    # Traiter les sélections de coordonnées restantes
    if coordinate_selects:
        converted_commands.append(convert_ts_coordinate_selects(coordinate_selects))
    
    # Retourner la première commande (ou les joindre si plusieurs)
    if len(converted_commands) == 1:
        return converted_commands[0]
    else:
        # Pour plusieurs commandes, on retourne la première et log les autres
        # (dans un vrai contexte, il faudrait les traiter séparément)
        return converted_commands[0]  # Simplification pour ce contexte

def convert_ts_coordinate_selects(coord_selects):
    """Convertit une liste de sélections de coordonnées pour TypeScript"""
    import re
    
    if len(coord_selects) == 1:
        # Une seule sélection
        coords = coord_selects[0]
        if re.match(r'\[\d+,\d+\s+\d+,\d+\]', coords):
            return f"addUnifiedCommand('SELECT', undefined, ['{coords}'])"
        elif re.match(r'\d+,\d+\s+\d+,\d+', coords):
            return f"addUnifiedCommand('SELECT', undefined, ['[{coords}]'])"
        else:
            return f"addUnifiedCommand('SELECT', undefined, ['[{coords}]'])"
    else:
        # Plusieurs sélections -> format unifié SELECT ([...] +[...])
        coord_parts = []
        for i, coords in enumerate(coord_selects):
            if re.match(r'\[\d+,\d+\s+\d+,\d+\]', coords):
                formatted_coords = coords
            elif re.match(r'\d+,\d+\s+\d+,\d+', coords):
                formatted_coords = f'[{coords}]'
            else:
                formatted_coords = f'[{coords}]'
            
            if i == 0:
                coord_parts.append(formatted_coords)
            else:
                coord_parts.append(f'+{formatted_coords}')
        
        unified_format = f"({' '.join(coord_parts)})"
        return f"addUnifiedCommand('SELECT', undefined, ['{unified_format}'])"

def convert_fill_command(params):
    """Convertit une commande FILL legacy vers format unifié"""
    # Exemple: "5 0,0 1,1" -> "FILL 5 [0,0 1,1]"
    parts = params.strip().split()
    if len(parts) >= 3:
        color = parts[0]  # "5"
        coords = ' '.join(parts[1:])  # "0,0 1,1"
        return f"addUnifiedCommand('FILL', '{color}', ['{coords}'])"
    return f"addUnifiedCommand('FILL', '', ['{params}'])"

def convert_clear_command(params):
    """Convertit une commande CLEAR legacy vers format unifié"""
    # Exemple: "0,0 1,1" -> "CLEAR [0,0 1,1]"
    coords = params.strip()
    return f"addUnifiedCommand('CLEAR', undefined, ['{coords}'])"

def convert_paste_command(params):
    """Convertit une commande PASTE legacy vers format unifié"""
    # Exemple: "0,0" -> "PASTE [0,0]"
    coords = params.strip()
    return f"addUnifiedCommand('PASTE', undefined, ['{coords}'])"

def convert_simple_command(action, params):
    """Convertit une commande simple vers format unifié"""
    if params:
        return f"addUnifiedCommand('{action}', '{params}')"
    else:
        return f"addUnifiedCommand('{action}')"

# === HELPERS DE CONVERSION MANQUANTS ===

def convert_surround_command(params):
    """Convertit une commande SURROUND legacy vers format unifié"""
    # Exemple: "3 0,0 1,1" -> "SURROUND 3 [0,0 1,1]"
    parts = params.strip().split()
    if len(parts) >= 3:
        color = parts[0]  # "3"
        coords = ' '.join(parts[1:])  # "0,0 1,1"
        return f"addUnifiedCommand('SURROUND', '{color}', ['{coords}'])"
    return f"addUnifiedCommand('SURROUND', '', ['{params}'])"

def convert_replace_command(params):
    """Convertit une commande REPLACE legacy vers format unifié"""
    # Exemple: "1,8,3 5 0,0 1,1" -> "REPLACE 1,8,3 5 [0,0 1,1]"
    parts = params.strip().split()
    if len(parts) >= 4:
        old_colors = parts[0]  # "1,8,3"
        new_color = parts[1]   # "5"
        coords = ' '.join(parts[2:])  # "0,0 1,1"
        return f"addUnifiedCommand('REPLACE', '{old_colors} {new_color}', ['{coords}'])"
    return f"addUnifiedCommand('REPLACE', '', ['{params}'])"

def convert_copy_command(params):
    """Convertit une commande COPY legacy vers format unifié"""
    # Exemple: "0,0 1,1" -> "COPY ([0,0 1,1])"
    coords = params.strip()
    return f"addUnifiedCommand('COPY', undefined, ['({coords})'])"

def convert_cut_command(params):
    """Convertit une commande CUT legacy vers format unifié"""
    # Exemple: "0,0 1,1" -> "CUT ([0,0 1,1])"
    coords = params.strip()
    return f"addUnifiedCommand('CUT', undefined, ['({coords})'])"

def convert_flip_command(direction, params):
    """Convertit une commande FLIP legacy vers format unifié"""
    # Exemple: HORIZONTAL "0,0 1,1" -> "FLIP HORIZONTAL ([0,0 1,1])"
    coords = params.strip()
    return f"addUnifiedCommand('FLIP {direction}', undefined, ['({coords})'])"

def convert_rotate_command(direction, params):
    """Convertit une commande ROTATE legacy vers format unifié"""
    # Exemple: LEFT "0,0 1,1" -> "ROTATE LEFT ([0,0 1,1])"
    coords = params.strip()
    return f"addUnifiedCommand('ROTATE {direction}', undefined, ['({coords})'])"

def convert_structure_command(action, target, position, params):
    """Convertit les commandes INSERT/DELETE vers format unifié"""
    # Exemple: INSERT ROWS BELOW "0,0 1,1" -> "INSERT 5 ROWS BELOW [0,0 1,1]"
    coords = params.strip() if params else ""
    
    if action == "INSERT" and position:
        # Pour INSERT, on peut avoir un nombre en début de params
        parts = coords.split()
        if parts and parts[0].isdigit():
            count = parts[0]
            coord_part = ' '.join(parts[1:]) if len(parts) > 1 else ""
            return f"addUnifiedCommand('INSERT {count} {target} {position}', undefined, ['{coord_part}'])"
        else:
            return f"addUnifiedCommand('INSERT 1 {target} {position}', undefined, ['{coords}'])"
    else:
        # DELETE ROWS/COLUMNS
        return f"addUnifiedCommand('{action} {target}', undefined, ['{coords}'])"

def convert_extract_command(params):
    """Convertit une commande EXTRACT legacy vers format unifié"""
    # Exemple: "4,4 6,6" -> "EXTRACT [4,4 6,6]"
    coords = params.strip()
    return f"addUnifiedCommand('EXTRACT', undefined, ['{coords}'])"

def convert_select_invert_command(params):
    """Convertit une commande SELECT_INVERT legacy vers format unifié"""
    # Exemple: "0,0 1,1" -> "SELECT_INVERT ([0,0 1,1])"
    coords = params.strip()
    return f"addUnifiedCommand('SELECT_INVERT', undefined, ['({coords})'])"

def convert_select_color_command(params):
    """Convertit une commande SELECT_COLOR legacy vers format unifié"""
    # Exemple: "1,2,3 0,0 1,1" -> "(SELECT_COLOR 1,2,3 [0,0 1,1])"
    parts = params.strip().split()
    if len(parts) >= 3:
        colors = parts[0]  # "1,2,3"
        coords = ' '.join(parts[1:])  # "0,0 1,1"
        return f"addUnifiedCommand('SELECT_COLOR', '({colors})', ['{coords}'])"
    return f"addUnifiedCommand('SELECT_COLOR', '', ['{params}'])"

def convert_transfert_command(params):
    """Convertit une commande TRANSFERT legacy vers format unifié"""
    # Exemple: "(EDIT 0,0 7)" -> "TRANSFERT {EDIT 7 [0,0]}"
    # On applique les conversions à l'intérieur du TRANSFERT
    inner_commands = params.strip()
    if inner_commands.startswith('(') and inner_commands.endswith(')'):
        inner_commands = inner_commands[1:-1]
    
    # Appliquer les conversions de base aux commandes internes
    inner_commands = re.sub(r'\bEDIT\s+(\d+),(\d+)\s+(\d+)', r'EDIT \3 [\1,\2]', inner_commands)
    inner_commands = re.sub(r'\bFILL\s+(\d+)\s+(\d+),(\d+)\s+(\d+),(\d+)', r'FILL \1 [\2,\3 \4,\5]', inner_commands)
    
    return f"addUnifiedCommand('TRANSFERT', undefined, ['{{{inner_commands}}}'])"

def migrate_automation_store_usage():
    """Met à jour le store d'automation pour utiliser la nouvelle interface"""
    store_file = 'frontend/src/hooks/useAutomationStore.ts'
    
    if not os.path.exists(store_file):
        print(f"❌ Store non trouvé: {store_file}")
        return False
    
    try:
        with open(store_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Vérifier que les nouvelles méthodes sont bien exportées
        if 'addUnifiedCommand' not in content:
            print(f"⚠️  Store manque addUnifiedCommand - utilisera les nouvelles méthodes du store créé")
        
        return True
    except Exception as e:
        print(f"❌ Erreur vérification store: {e}")
        return False

def create_migration_summary():
    """Crée un résumé de la migration"""
    summary = """
# 🚀 MIGRATION VERS COMMANDES UNIFIÉES - RÉSUMÉ

## ✅ Conversions Appliquées

### Fichiers de Code TypeScript (.tsx/.ts)
#### Format EDIT
- **Avant**: `addCommand('EDIT', '0,0 7')`
- **Après**: `addUnifiedCommand('EDIT', '7', ['0,0'])`

#### Format SELECT
- **Avant**: `addCommand('SELECT', '0,0 1,1')`
- **Après**: `addUnifiedCommand('SELECT', undefined, ['0,0 1,1'])`

#### Format FILL
- **Avant**: `addCommand('FILL', '5 0,0 1,1')`
- **Après**: `addUnifiedCommand('FILL', '5', ['0,0 1,1'])`

#### Format CLEAR
- **Avant**: `addCommand('CLEAR', '0,0 1,1')`
- **Après**: `addUnifiedCommand('CLEAR', undefined, ['0,0 1,1'])`

#### Format PASTE
- **Avant**: `addCommand('PASTE', '0,0')`
- **Après**: `addUnifiedCommand('PASTE', undefined, ['0,0'])`

### Fichiers de Scénarios (.agi)
#### Format EDIT
- **Avant**: `EDIT 0,0 7`
- **Après**: `EDIT 7 [0,0]`

#### Format SELECT
- **Avant**: `SELECT 0,0 1,1`
- **Après**: `SELECT [0,0 1,1]`

#### Format FILL
- **Avant**: `FILL 5 0,0 1,1`
- **Après**: `FILL 5 [0,0 1,1]`

#### Format CLEAR
- **Avant**: `CLEAR 0,0 1,1`
- **Après**: `CLEAR [0,0 1,1]`

#### Format PASTE
- **Avant**: `PASTE 0,0`
- **Après**: `PASTE [0,0]`

## 🎯 Résultat Attendu

### Dans le Code
Maintenant vos commandes seront générées au format unifié :
- ✅ `EDIT 7 [0,0]`
- ✅ `SELECT [0,0 1,1]`
- ✅ `FILL 5 [0,0 1,1]`
- ✅ `CLEAR [0,0 1,1]`
- ✅ `PASTE [0,0]`

### Dans les Fichiers .agi
Les scénarios utiliseront le format de coordonnées unifié [ligne,colonne] :
- ✅ Compatible avec l'IA et les modèles
- ✅ Format cohérent dans toute l'application
- ✅ Meilleure lisibilité et maintenance

## 🔒 Sécurité

- **Fichiers .agi** : Backups automatiques créés (.agi.backup)
- **Fichiers code** : Versioning Git recommandé
- **Validation** : Vérification du format avant migration

## 📋 Prochaines Étapes

1. Redémarrez le serveur de développement
2. Testez "Transférer vers Résolution"
3. Vérifiez le format des commandes générées
4. Validez les fichiers .agi migrés
"""
    
    with open('MIGRATION_SUMMARY.md', 'w', encoding='utf-8') as f:
        f.write(summary)
    
    print("📄 Résumé créé: MIGRATION_SUMMARY.md")

def main():
    """Fonction principale"""
    import sys
    import argparse
    
    # Parser d'arguments
    parser = argparse.ArgumentParser(
        description='Migration vers les commandes unifiées',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Exemples d'utilisation:
  python migrate_to_unified_commands.py --dry-run
  python migrate_to_unified_commands.py --task-id 00d62c1b
  python migrate_to_unified_commands.py --subset training --task-id 00d62c1b
  python migrate_to_unified_commands.py --subset evaluation --skip-code
        """
    )
    
    parser.add_argument(
        '--dry-run', 
        action='store_true',
        help='Simule la migration sans modifier les fichiers'
    )
    parser.add_argument(
        '--task-id',
        type=str,
        help='ID de la tâche spécifique à migrer (ex: 00d62c1b)'
    )
    parser.add_argument(
        '--subset',
        type=str,
        choices=['training', 'evaluation'],
        help='Subset spécifique à traiter (training ou evaluation)'
    )
    parser.add_argument(
        '--skip-code',
        action='store_true',
        help='Ignorer les fichiers de code TypeScript/React'
    )
    parser.add_argument(
        '--skip-agi',
        action='store_true',
        help='Ignorer les fichiers .agi'
    )
    parser.add_argument(
        '--max-files',
        type=int,
        default=None,
        help='Nombre maximum de fichiers à traiter (pour tests)'
    )
    
    args = parser.parse_args()
    
    # Configuration
    dry_run = args.dry_run
    task_id = args.task_id
    subset = args.subset
    skip_code = args.skip_code
    skip_agi = args.skip_agi
    max_files = args.max_files
    
    if dry_run:
        print("🔍 MODE SIMULATION - Aucune modification ne sera effectuée")
        print("="*60)
    else:
        print("🚀 Migration vers les commandes unifiées...")
        print("="*60)
    
    if task_id:
        print(f"🎯 Filtrage par tâche: {task_id}")
    if subset:
        print(f"📁 Filtrage par subset: {subset}")
    if max_files:        print(f"📊 Limitation: {max_files} fichiers maximum")
    
    # Fichiers à migrer
    files_to_migrate = []
    agi_files_to_check = []
    
    # === FICHIERS DE CODE TypeScript/React ===
    if not skip_code:
        code_patterns = [
            'frontend/src/components/resolution/**/*.tsx',
            'frontend/src/components/resolution/**/*.ts',
            'frontend/src/components/prompt/**/*.tsx',
            'frontend/src/components/notebook/**/*.tsx'
        ]
        
        for pattern in code_patterns:
            files_to_migrate.extend(glob.glob(pattern, recursive=True))
        
        if max_files and len(files_to_migrate) > max_files:
            files_to_migrate = files_to_migrate[:max_files]
    
    # === FICHIERS .AGI dans arcdata ===
    if not skip_agi:
        # Construire les patterns selon les filtres
        if subset and task_id:
            # Tâche spécifique dans subset spécifique
            agi_patterns = [f'arcdata/{subset}/{task_id}_*.agi']
        elif subset:
            # Tout le subset
            agi_patterns = [f'arcdata/{subset}/**/*.agi']
        elif task_id:
            # Tâche spécifique dans tous les subsets
            agi_patterns = [f'arcdata/**/{task_id}_*.agi']
        else:
            # Tous les fichiers .agi
            agi_patterns = ['arcdata/**/*.agi']
        
        for pattern in agi_patterns:
            found_files = glob.glob(pattern, recursive=True)
            agi_files_to_check.extend(found_files)
        
        if max_files and len(agi_files_to_check) > max_files:
            agi_files_to_check = agi_files_to_check[:max_files]    
    print(f"📁 Fichiers de code trouvés: {len(files_to_migrate)}")
    print(f"📄 Fichiers .agi trouvés: {len(agi_files_to_check)}")
    
    # Migrer les fichiers de code
    print("\n🔧 Migration des fichiers de code...")
    code_migrated_count = 0
    for file_path in files_to_migrate:
        if migrate_file(file_path, dry_run):
            code_migrated_count += 1
    
    # Vérifier/Migrer les fichiers .agi
    print("\n📄 Vérification des fichiers .agi...")
    agi_migrated_count = 0
    agi_already_unified = 0
      # Échantillonner quelques fichiers .agi pour analyse
    sample_size = min(10, len(agi_files_to_check))
    sample_files = agi_files_to_check[:sample_size]
    processed_files = set()  # Garder trace des fichiers déjà traités
    
    for file_path in sample_files:
        result = migrate_file(file_path, dry_run)
        processed_files.add(file_path)  # Marquer comme traité
        if result:
            agi_migrated_count += 1
        else:
            # Vérifier si c'est parce qu'il est déjà unifié
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                if '[' in content and ']' in content:
                    agi_already_unified += 1
            except:
                pass
    
    # Si l'échantillon est déjà unifié, pas besoin de traiter tous les fichiers
    if agi_already_unified == sample_size:
        print(f"✅ Tous les fichiers .agi sont déjà au format unifié (échantillon: {sample_size})")
        
        # Vérifier un échantillon plus large pour confirmer
        larger_sample = min(50, len(agi_files_to_check))
        all_unified = True
        
        for file_path in agi_files_to_check[:larger_sample]:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                if not ('[' in content and ']' in content):
                    all_unified = False
                    break
            except:
                pass        
        if all_unified:
            print(f"✅ Confirmation: échantillon élargi ({larger_sample} fichiers) déjà unifié")
        else:
            print("⚠️  Certains fichiers .agi nécessitent une migration")
            # Traiter tous les fichiers non encore traités
            for file_path in agi_files_to_check:
                if file_path not in processed_files:  # Éviter de retraiter
                    if migrate_file(file_path, dry_run):
                        agi_migrated_count += 1
    else:
        # Traiter tous les fichiers .agi non encore traités
        print(f"🔄 Migration de tous les fichiers .agi...")
        for file_path in agi_files_to_check:
            if file_path not in processed_files:  # Éviter de retraiter
                if migrate_file(file_path, dry_run):
                    agi_migrated_count += 1
    
    # Vérifier le store
    store_ok = migrate_automation_store_usage()
    
    # Créer le résumé
    create_migration_summary()
    print("\n" + "="*60)
    print("📊 RÉSUMÉ DE LA MIGRATION")
    print("="*60)
    print(f"Fichiers de code vérifiés: {len(files_to_migrate)}")
    print(f"Fichiers de code migrés: {code_migrated_count}")
    print(f"Fichiers .agi vérifiés: {len(agi_files_to_check) if agi_migrated_count > 0 or agi_already_unified < sample_size else sample_size}")
    print(f"Fichiers .agi migrés: {agi_migrated_count}")
    print(f"Fichiers .agi déjà unifiés: {agi_already_unified if agi_already_unified < sample_size else len(agi_files_to_check)}")
    print(f"Store validé: {'✅' if store_ok else '❌'}")
    
    total_migrated = code_migrated_count + agi_migrated_count
    
    if total_migrated > 0:
        if dry_run:
            print("\n🔍 SIMULATION TERMINÉE - Aucune modification effectuée")
            print("💡 Pour appliquer les changements, relancez sans --dry-run:")
            print("   python migrate_to_unified_commands.py")
        else:
            print("\n🎉 Migration réussie !")
            print("💡 Prochaines étapes:")
            print("   1. Redémarrez le serveur: cd frontend && npm run dev")
            print("   2. Testez 'Transférer vers Résolution'")
            print("   3. Vérifiez le nouveau format dans l'éditeur")
            if agi_migrated_count > 0:
                print(f"   4. Vérifiez les backups .agi.backup créés")
    else:
        if dry_run:
            print("\n ℹ️ Simulation : Aucune migration nécessaire (déjà à jour)")
        else:
            print("\n ℹ️ Aucune migration nécessaire (déjà à jour)")
    
    return total_migrated > 0

if __name__ == '__main__':
    main()