// src/components/resolution/utils/clipboardTransformUtils.ts
import { type WritableDraft } from 'immer';
import { type GridContent, type ClipboardState as ClipboardStoreStateType, type Position } from '../types/clipboardTypes';
import { type SelectionState } from '../hooks/useSelection';
import { type AutomationState } from '../hooks/useAutomationStore';
import { Grid } from '../../../lib/grid'; // Ajout pour les nouvelles fonctions
import { coordToString } from './selectionFormatUtils'; // Ajout pour extractClipboardDataFromGrid

// Type pour la fonction set de Zustand utilisée avec un updater immer qui retourne void
type ImmerVoidUpdaterFn = (draft: WritableDraft<ClipboardStoreStateType>) => void;
type ZustandSetFn = (updater: ImmerVoidUpdaterFn) => void;

// Type pour la fonction get de Zustand
type ZustandGetFn = () => ClipboardStoreStateType;


/**
 * Extrait les données et le masque pour le presse-papiers à partir d'une grille et d'une sélection.
 * @param grid La grille source.
 * @param bounds Les limites de la zone à copier { minRow, maxRow, minCol, maxCol }.
 * @param selectedCells L'ensemble des cellules sélectionnées (format "row,col").
 * @returns Un objet contenant `data`, `mask`, `width`, et `height` pour le presse-papiers.
 */
export const extractClipboardDataFromGrid = (
  grid: Grid,
  bounds: { minRow: number; maxRow: number; minCol: number; maxCol: number },
  selectedCells: Set<string>
): { data: number[][]; mask: boolean[][]; width: number; height: number } => {
  const { minRow, maxRow, minCol, maxCol } = bounds;
  const height = maxRow - minRow + 1;
  const width = maxCol - minCol + 1;

  const data: number[][] = [];
  const mask: boolean[][] = [];

  // Si aucune cellule n'est sélectionnée, considérer toute la zone comme sélectionnée
  const hasSelection = selectedCells.size > 0;

  for (let r = 0; r < height; r++) {
    data[r] = [];
    mask[r] = [];
    for (let c = 0; c < width; c++) {
      const gridRow = minRow + r;
      const gridCol = minCol + c;
      const cellKey = coordToString(gridRow, gridCol);

      // Si pas de sélection active, toutes les cellules sont considérées comme sélectionnées
      const isSelected = hasSelection ? selectedCells.has(cellKey) : true;
      mask[r][c] = isSelected;

      const value = grid.getCell(gridRow, gridCol) ?? 0;
      data[r][c] = value;
    }
    
    // Validation de la ligne créée
    if (data[r].length !== width || mask[r].length !== width) {
      console.error(`[ClipboardTransform] Erreur de dimension à la ligne ${r}: data=${data[r].length}, mask=${mask[r].length}, width attendu=${width}`);
    }
  }
  // Validation finale pour s'assurer de la cohérence des données
  if (data.length !== height) {
    console.error('[ClipboardTransform] Incohérence détectée dans extractClipboardDataFromGrid:', {
      dataLength: data.length,
      expectedHeight: height,
      bounds,
      selectedCellsCount: selectedCells.size
    });
    // Corriger la hauteur pour correspondre aux données réelles
    const correctedHeight = data.length;
    return { data, mask: mask.slice(0, correctedHeight), width, height: correctedHeight };
  }
  
  return { data, mask, width, height };
};

/**
 * Applique le contenu du presse-papiers (données et masque) à une grille cible.
 * @param targetGrid La grille sur laquelle coller (sera modifiée).
 * @param clipboardContent Le contenu du presse-papiers.
 * @param pasteRow La ligne de destination pour le coin supérieur gauche du contenu.
 * @param pasteCol La colonne de destination pour le coin supérieur gauche du contenu.
 * @returns La grille cible modifiée.
 */
export const applyClipboardContentToGrid = (
  targetGrid: Grid,
  clipboardContent: GridContent,
  pasteRow: number,
  pasteCol: number
): Grid => {
  // Validation des données du presse-papiers
  if (!clipboardContent || !clipboardContent.data || !Array.isArray(clipboardContent.data)) {
    console.error('[ClipboardTransform] Données du presse-papiers invalides:', clipboardContent);
    return targetGrid;
  }
  
  if (clipboardContent.data.length !== clipboardContent.height) {
    console.error('[ClipboardTransform] Incohérence entre data.length et height:', {
      dataLength: clipboardContent.data.length,
      height: clipboardContent.height
    });
    return targetGrid;
  }
  for (let r = 0; r < clipboardContent.height; r++) {
    // Vérifier que la ligne existe dans les données
    if (!clipboardContent.data[r]) {
      console.warn(`[ClipboardTransform] Ligne ${r} manquante dans clipboardContent.data`);
      continue;
    }
    
    for (let c = 0; c < clipboardContent.width; c++) {
      const gridRow = pasteRow + r;
      const gridCol = pasteCol + c;
      const valueToPaste = clipboardContent.data[r][c];

      if (
        gridRow >= 0 &&
        gridRow < targetGrid.height &&
        gridCol >= 0 &&
        gridCol < targetGrid.width
      ) {
        // Appliquer le masque: si pas de masque, tout est collé. Sinon, seulement les cellules masquées.
        const shouldPaste = clipboardContent.mask && clipboardContent.mask[r] ? clipboardContent.mask[r][c] : true;
        if (shouldPaste) {
          // La valeur -1 est parfois utilisée pour "transparent" ou "ne pas coller"
          if (valueToPaste !== -1) {
            targetGrid.setCell(gridRow, gridCol, valueToPaste);
          }
        }
      }
    }
  }
  return targetGrid;
};


/**
 * Applique une rotation à droite (90° dans le sens horaire) au contenu du presse-papiers.
 * Met également à jour la sélection et enregistre une commande d'automatisation.
 */
export const rotateClipboardRight = (
  set: ZustandSetFn,
  get: any,
  selectionStore: any,
  automationStore: AutomationState
): void => {
  set((draft) => {
    const content = draft.content;

    if (!content) return;

    // Validation des données
    if (!content.data || !Array.isArray(content.data) || content.data.length !== content.height) {
      console.error('[ClipboardTransform] Données invalides pour rotation droite:', content);
      return;
    }

    const height = content.height;
    const width = content.width;

    const rotatedData: number[][] = [];
    const rotatedMask: boolean[][] = [];

    for (let col = 0; col < width; col++) {
      rotatedData[col] = [];
      rotatedMask[col] = [];
      for (let row = 0; row < height; row++) {
        // Vérifier que la ligne existe
        if (!content.data[row]) {
          console.warn(`[ClipboardTransform] Ligne ${row} manquante dans rotation droite`);
          continue;
        }
        rotatedData[col][height - 1 - row] = content.data[row][col];
        if (content.mask && content.mask[row]) {
          rotatedMask[col][height - 1 - row] = content.mask[row][col];
        } else {
          // Si pas de masque, considérer toutes les cellules comme faisant partie du masque
          rotatedMask[col][height - 1 - row] = true;
        }
      }
    }

    const newContent: GridContent = {
      ...content,
      data: rotatedData,
      mask: rotatedMask,
      width: height, // Les dimensions s'inversent
      height: width,
      timestamp: Date.now()
    };

    draft.content = newContent;

    // Mettre à jour la sélection
    try {
      const selectionBounds = selectionStore.getSelectionBounds();
      if (selectionBounds) {
        selectionStore.transformSelection('rotateRight', selectionBounds);
      }
    } catch (error) {
      console.error('[ClipboardTransformUtils] Failed to update selection for rotateRight:', error);
    }

    // Ajouter une commande d'automatisation
    try {
      if (draft.currentPosition) {
        const { row, col } = draft.currentPosition;
        const selectionBounds = selectionStore.getSelectionBounds();
        if (selectionBounds) {
          // Après rotation, la largeur devient la hauteur et vice versa pour la zone affectée
          automationStore.addCommand('ROTATE', `RIGHT [${row},${col} ${row + height - 1},${col + width - 1}]`);
        } else {
          // Fallback si pas de sélection (utilise les dimensions du contenu avant rotation)
          automationStore.addCommand('ROTATE', `LEFT [${row},${col} ${row + height - 1},${col + width - 1}]`);
        }
      }
    } catch (error) {
      console.error('[ClipboardTransformUtils] Failed to add automation command for rotateRight:', error);
    }
  });
};

/**
 * Applique une rotation à gauche (90° dans le sens antihoraire) au contenu du presse-papiers.
 */
export const rotateClipboardLeft = (
  set: ZustandSetFn,
  get: any,
  selectionStore: any,
  automationStore: AutomationState
): void => {
  set((draft) => {
    const content = draft.content;

    if (!content) return;

    // Validation des données
    if (!content.data || !Array.isArray(content.data) || content.data.length !== content.height) {
      console.error('[ClipboardTransform] Données invalides pour rotation gauche:', content);
      return;
    }

    const height = content.height;
    const width = content.width;

    const rotatedData: number[][] = [];
    const rotatedMask: boolean[][] = [];

    // Rotation LEFT (antihoraire) : transposer puis inverser les lignes
    for (let row = 0; row < height; row++) {
      // Vérifier que la ligne existe
      if (!content.data[row]) {
        console.warn(`[ClipboardTransform] Ligne ${row} manquante dans rotation gauche`);
        continue;
      }
      
      rotatedData[height - 1 - row] = [];
      rotatedMask[height - 1 - row] = [];
      for (let col = 0; col < width; col++) {
        rotatedData[height - 1 - row][col] = content.data[row][col];
        if (content.mask && content.mask[row]) {
          rotatedMask[height - 1 - row][col] = content.mask[row][col];
        } else {
          rotatedMask[height - 1 - row][col] = true;
        }
      }
    }

    const newContent: GridContent = {
      ...content,
      data: rotatedData,
      mask: rotatedMask,
      width: height,
      height: width,
      timestamp: Date.now()
    };

    draft.content = newContent;

    try {
      const selectionBounds = selectionStore.getSelectionBounds();
      if (selectionBounds) {
        selectionStore.transformSelection('rotateLeft', selectionBounds);
      }
    } catch (error) {
      console.error('[ClipboardTransformUtils] Failed to update selection for rotateLeft:', error);
    }

    try {
      if (draft.currentPosition) {
        const { row, col } = draft.currentPosition;
        const selectionBounds = selectionStore.getSelectionBounds();
        if (selectionBounds) {
          const selectionWidth = selectionBounds.endCol - selectionBounds.startCol + 1;
          const selectionHeight = selectionBounds.endRow - selectionBounds.startRow + 1;
          automationStore.addCommand('ROTATE', `LEFT ([${row},${col} ${row + selectionHeight - 1},${col + selectionWidth - 1}])`);
        } else {
          automationStore.addCommand('ROTATE', `LEFT ([${row},${col} ${row + height - 1},${col + width - 1}])`);
        }
      }
    } catch (error) {
      console.error('[ClipboardTransformUtils] Failed to add automation command for rotateLeft:', error);
    }
  });
};

/**
 * Applique une symétrie horizontale au contenu du presse-papiers.
 */
export const flipClipboardHorizontal = (
  set: ZustandSetFn,
  get: any,
  selectionStore: any,
  automationStore: AutomationState
): void => {
  set((draft) => {
    const content = draft.content;

    if (!content) return;

    // Validation des données
    if (!content.data || !Array.isArray(content.data) || content.data.length !== content.height) {
      console.error('[ClipboardTransform] Données invalides pour flip horizontal:', content);
      return;
    }

    const height = content.height;
    const width = content.width;

    const flippedData: number[][] = [];
    const flippedMask: boolean[][] = [];

    for (let row = 0; row < height; row++) {
      // Vérifier que la ligne existe
      if (!content.data[row]) {
        console.warn(`[ClipboardTransform] Ligne ${row} manquante dans flip horizontal`);
        continue;
      }
      
      flippedData[row] = [];
      flippedMask[row] = [];
      for (let col = 0; col < width; col++) {
        flippedData[row][width - 1 - col] = content.data[row][col];
        if (content.mask && content.mask[row]) {
          flippedMask[row][width - 1 - col] = content.mask[row][col];
        } else {
          flippedMask[row][width - 1 - col] = true;
        }
      }
    }

    const newContent: GridContent = {
      ...content,
      data: flippedData,
      mask: flippedMask,
      // width et height ne changent pas
      timestamp: Date.now()
    };

    draft.content = newContent;

    try {
      const selectionBounds = selectionStore.getSelectionBounds();
      if (selectionBounds) {
        selectionStore.transformSelection('flipHorizontal', selectionBounds);
      }
    } catch (error) {
      console.error('[ClipboardTransformUtils] Failed to update selection for flipHorizontal:', error);
    }

    try {
      if (draft.currentPosition) {
        const { row, col } = draft.currentPosition;
        const selectionBounds = selectionStore.getSelectionBounds();
        if (selectionBounds) {
          automationStore.addCommand('FLIP', `HORIZONTAL ([${row},${col} ${row + height - 1},${col + width - 1}])`);
        } else {
          automationStore.addCommand('FLIP', `HORIZONTAL ([${row},${col} ${row + height - 1},${col + width - 1}])`);
        }
      }
    } catch (error) {
      console.error('[ClipboardTransformUtils] Failed to add automation command for flipHorizontal:', error);
    }
  });
};

/**
 * Applique une symétrie verticale au contenu du presse-papiers.
 */
export const flipClipboardVertical = (
  set: ZustandSetFn,
  get: any,
  selectionStore: any,
  automationStore: AutomationState
): void => {
  set((draft) => {
    const content = draft.content;

    if (!content) return;

    // Validation des données
    if (!content.data || !Array.isArray(content.data) || content.data.length !== content.height) {
      console.error('[ClipboardTransform] Données invalides pour flip vertical:', content);
      return;
    }

    const height = content.height;
    const width = content.width;

    const flippedData: number[][] = [];
    const flippedMask: boolean[][] = [];

    for (let row = 0; row < height; row++) {
      // Vérifier que la ligne existe
      if (!content.data[row]) {
        console.warn(`[ClipboardTransform] Ligne ${row} manquante dans flip vertical`);
        continue;
      }
      
      flippedData[height - 1 - row] = []; // La ligne est inversée
      flippedMask[height - 1 - row] = [];
      for (let col = 0; col < width; col++) {
        flippedData[height - 1 - row][col] = content.data[row][col];
        if (content.mask && content.mask[row]) {
          flippedMask[height - 1 - row][col] = content.mask[row][col];
        } else {
          flippedMask[height - 1 - row][col] = true;
        }
      }
    }

    const newContent: GridContent = {
      ...content,
      data: flippedData,
      mask: flippedMask,
      // width et height ne changent pas
      timestamp: Date.now()
    };

    draft.content = newContent;

    try {
      const selectionBounds = selectionStore.getSelectionBounds();
      if (selectionBounds) {
        selectionStore.transformSelection('flipVertical', selectionBounds);
      }
    } catch (error) {
      console.error('[ClipboardTransformUtils] Failed to update selection for flipVertical:', error);
    }

    try {
      if (draft.currentPosition) {
        const { row, col } = draft.currentPosition;
        const selectionBounds = selectionStore.getSelectionBounds();
        if (selectionBounds) {
          const selectionWidth = selectionBounds.endCol - selectionBounds.startCol + 1;
          const selectionHeight = selectionBounds.endRow - selectionBounds.startRow + 1;
          automationStore.addCommand('FLIP', `VERTICAL ([${row},${col} ${row + selectionHeight - 1},${col + selectionWidth - 1}])`);
        } else {
          automationStore.addCommand('FLIP', `VERTICAL ([${row},${col} ${row + height - 1},${col + width - 1}])`);
        }
      }
    } catch (error) {
      console.error('[ClipboardTransformUtils] Failed to add automation command for flipVertical:', error);
    }
  });
};

/**
 * Déplace le contenu du presse-papiers vers le haut.
 */
export const moveClipboardUp = (
  set: ZustandSetFn,
  get: ZustandGetFn,
  selectionStore: any,
  automationStore: AutomationState
): void => {
  console.log('[clipboardTransformUtils] moveClipboardUp called');
  set((draft) => {
    if (draft.currentPosition && draft.content) {
      const oldPosition: Position = { ...draft.currentPosition };
      draft.currentPosition.row = Math.max(0, draft.currentPosition.row - 1);
      draft.content = { ...draft.content, timestamp: Date.now() };

      console.log('[clipboardTransformUtils] Moving from', oldPosition, 'to', draft.currentPosition);
      
      try {
        // Note: selectionStore est l'état complet, pas seulement getState()
        console.log('[clipboardTransformUtils] Calling moveSelection with selectionStore:', selectionStore);
        console.log('[clipboardTransformUtils] selectionStore type:', typeof selectionStore);
        console.log('[clipboardTransformUtils] selectionStore.moveSelection type:', typeof selectionStore.moveSelection);
        console.log('[clipboardTransformUtils] selectionStore keys:', Object.keys(selectionStore));
        selectionStore.moveSelection('up', oldPosition, draft.currentPosition);
        console.log('[clipboardTransformUtils] moveSelection completed');
      } catch (error) {
        console.error('[ClipboardTransformUtils] Failed to update selection for moveUp:', error);
      }

      // Note: Pas d'enregistrement de commande d'automatisation pour les déplacements
      // Seules les positions d'origine et d'arrivée sont importantes pour rejouer le scénario
    } else {
      console.log('[clipboardTransformUtils] No currentPosition or content');
    }
  });
};

/**
 * Déplace le contenu du presse-papiers vers le bas.
 */
export const moveClipboardDown = (
  set: ZustandSetFn,
  get: ZustandGetFn,
  selectionStore: any,
  automationStore: AutomationState
): void => {
  set((draft) => {
    if (draft.currentPosition && draft.content) {
      const oldPosition: Position = { ...draft.currentPosition };
      // La logique de maxRow doit être gérée par l'appelant ou provenir d'une source de vérité (ex: dimensions de la grille)
      // Pour l'instant, on conserve la logique originale qui pourrait être restrictive.
      // Idéalement, la grille de destination devrait fournir ses dimensions.
      const maxRow = 30 - draft.content.height; // Valeur arbitraire, à ajuster
      draft.currentPosition.row = Math.min(maxRow, draft.currentPosition.row + 1);
      draft.content = { ...draft.content, timestamp: Date.now() };

      try {
        selectionStore.moveSelection('down', oldPosition, draft.currentPosition);
      } catch (error) {
        console.error('[ClipboardTransformUtils] Failed to update selection for moveDown:', error);
      }

      // Note: Pas d'enregistrement de commande d'automatisation pour les déplacements
      // Seules les positions d'origine et d'arrivée sont importantes pour rejouer le scénario
    }
  });
};

/**
 * Déplace le contenu du presse-papiers vers la gauche.
 */
export const moveClipboardLeft = (
  set: ZustandSetFn,
  get: ZustandGetFn,
  selectionStore: any,
  automationStore: AutomationState
): void => {
  set((draft) => {
    if (draft.currentPosition && draft.content) {
      const oldPosition: Position = { ...draft.currentPosition };
      draft.currentPosition.col = Math.max(0, draft.currentPosition.col - 1);
      draft.content = { ...draft.content, timestamp: Date.now() };

      try {
        selectionStore.moveSelection('left', oldPosition, draft.currentPosition);
      } catch (error) {
        console.error('[ClipboardTransformUtils] Failed to update selection for moveLeft:', error);
      }

      // Note: Pas d'enregistrement de commande d'automatisation pour les déplacements
      // Seules les positions d'origine et d'arrivée sont importantes pour rejouer le scénario
    }
  });
};

/**
 * Déplace le contenu du presse-papiers vers la droite.
 */
export const moveClipboardRight = (
  set: ZustandSetFn,
  get: ZustandGetFn,
  selectionStore: any,
  automationStore: AutomationState
): void => {
  set((draft) => {
    if (draft.currentPosition && draft.content) {
      const oldPosition: Position = { ...draft.currentPosition };
      // La logique de maxCol doit être gérée par l'appelant ou provenir d'une source de vérité
      const maxCol = 30 - draft.content.width; // Valeur arbitraire, à ajuster
      draft.currentPosition.col = Math.min(maxCol, draft.currentPosition.col + 1);
      draft.content = { ...draft.content, timestamp: Date.now() };

      try {
        selectionStore.moveSelection('right', oldPosition, draft.currentPosition);
      } catch (error) {
        console.error('[ClipboardTransformUtils] Failed to update selection for moveRight:', error);
      }

      // Note: Pas d'enregistrement de commande d'automatisation pour les déplacements
      // Seules les positions d'origine et d'arrivée sont importantes pour rejouer le scénario
    }
  });
};

/**
 * Multiplie le contenu du presse-papiers par un facteur donné.
 * Chaque cellule est répliquée factor x factor fois.
 */
export const multiplyClipboard = (
  factor: number,
  set: ZustandSetFn,
  get: any,
  selectionStore: any,
  automationStore: AutomationState
): void => {
  set((draft) => {
    const content = draft.content;

    if (!content || factor < 2 || factor > 10) {
      console.error('[ClipboardTransform] Paramètres invalides pour multiplication:', { content, factor });
      return;
    }

    // Validation des données
    if (!content.data || !Array.isArray(content.data) || content.data.length !== content.height) {
      console.error('[ClipboardTransform] Données invalides pour multiplication:', content);
      return;
    }

    const height = content.height;
    const width = content.width;
    const newHeight = height * factor;
    const newWidth = width * factor;

    const multipliedData: number[][] = [];
    const multipliedMask: boolean[][] = [];

    // Créer la nouvelle grille multipliée : répéter le motif entier factor x factor fois
    // MAIS le masque doit indiquer où appliquer le motif (zones qui étaient sélectionnées dans l'original)
    for (let i = 0; i < height; i++) {
      for (let fi = 0; fi < factor; fi++) {
        const newRowIndex = i * factor + fi;
        multipliedData[newRowIndex] = [];
        multipliedMask[newRowIndex] = [];

        for (let j = 0; j < width; j++) {
          const originalMaskValue = content.mask && content.mask[i] ? content.mask[i][j] : true;

          for (let fj = 0; fj < factor; fj++) {
            const newColIndex = j * factor + fj;

            // Pour les données : répéter le motif entier
            const dataRow = fi;
            const dataCol = fj;
            multipliedData[newRowIndex][newColIndex] = content.data[dataRow][dataCol];

            // Pour le masque : chaque cellule du masque original devient un bloc factor x factor
            // avec la même valeur (true si la cellule originale était sélectionnée)
            multipliedMask[newRowIndex][newColIndex] = originalMaskValue;
          }
        }
      }
    }

    const newContent: GridContent = {
      ...content,
      data: multipliedData,
      mask: multipliedMask,
      width: newWidth,
      height: newHeight,
      timestamp: Date.now()
    };

    draft.content = newContent;

    // Mettre à jour la sélection
    try {
      const selectionBounds = selectionStore.getSelectionBounds();
      if (selectionBounds) {
        selectionStore.transformSelection('multiply', selectionBounds, factor);
      }
    } catch (error) {
      console.error('[ClipboardTransformUtils] Failed to update selection for multiply:', error);
    }

    // Ajouter une commande d'automatisation
    try {
      if (draft.currentPosition) {
        const { row, col } = draft.currentPosition;
        const selectionBounds = selectionStore.getSelectionBounds();
        if (selectionBounds) {
          automationStore.addCommand('MULTIPLY', `${factor} [${row},${col} ${row + height - 1},${col + width - 1}]`);
        } else {
          automationStore.addCommand('MULTIPLY', `${factor} [${row},${col} ${row + height - 1},${col + width - 1}]`);
        }
      }
    } catch (error) {
      console.error('[ClipboardTransformUtils] Failed to add automation command for multiply:', error);
    }
  });
};

/**
 * Divise le contenu du presse-papiers par un facteur donné.
 * Prend un échantillon de chaque bloc factor x factor.
 */
export const divideClipboard = (
  factor: number,
  set: ZustandSetFn,
  get: any,
  selectionStore: any,
  automationStore: AutomationState
): void => {
  set((draft) => {
    const content = draft.content;

    if (!content || factor < 2 || factor > 10) {
      console.error('[ClipboardTransform] Paramètres invalides pour division:', { content, factor });
      return;
    }

    // Validation des données
    if (!content.data || !Array.isArray(content.data) || content.data.length !== content.height) {
      console.error('[ClipboardTransform] Données invalides pour division:', content);
      return;
    }

    const height = content.height;
    const width = content.width;

    // Vérifier que les dimensions sont divisibles par le facteur
    if (height % factor !== 0 || width % factor !== 0) {
      console.error(`[ClipboardTransform] Les dimensions ${height}x${width} ne sont pas divisibles par ${factor}`);
      return;
    }

    const newHeight = height / factor;
    const newWidth = width / factor;

    const dividedData: number[][] = [];
    const dividedMask: boolean[][] = [];

    // Créer la nouvelle grille divisée
    for (let i = 0; i < newHeight; i++) {
      dividedData[i] = [];
      dividedMask[i] = [];

      for (let j = 0; j < newWidth; j++) {
        // Prendre la valeur du coin supérieur gauche de chaque bloc factor x factor
        const sourceRow = i * factor;
        const sourceCol = j * factor;

        dividedData[i][j] = content.data[sourceRow][sourceCol];
        dividedMask[i][j] = content.mask && content.mask[sourceRow] ? content.mask[sourceRow][sourceCol] : true;
      }
    }

    const newContent: GridContent = {
      ...content,
      data: dividedData,
      mask: dividedMask,
      width: newWidth,
      height: newHeight,
      timestamp: Date.now()
    };

    draft.content = newContent;

    // Mettre à jour la sélection
    try {
      const selectionBounds = selectionStore.getSelectionBounds();
      if (selectionBounds) {
        selectionStore.transformSelection('divide', selectionBounds, factor);
      }
    } catch (error) {
      console.error('[ClipboardTransformUtils] Failed to update selection for divide:', error);
    }

    // Ajouter une commande d'automatisation
    try {
      if (draft.currentPosition) {
        const { row, col } = draft.currentPosition;
        const selectionBounds = selectionStore.getSelectionBounds();
        if (selectionBounds) {
          automationStore.addCommand('DIVIDE', `${factor} [${row},${col} ${row + newHeight - 1},${col + newWidth - 1}]`);
        } else {
          automationStore.addCommand('DIVIDE', `${factor} [${row},${col} ${row + newHeight - 1},${col + newWidth - 1}]`);
        }
      }
    } catch (error) {
      console.error('[ClipboardTransformUtils] Failed to add automation command for divide:', error);
    }
  });
};
