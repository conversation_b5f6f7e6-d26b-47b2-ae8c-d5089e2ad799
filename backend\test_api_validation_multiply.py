#!/usr/bin/env python
"""
Test de validation API pour les commandes MULTIPLY et DIVIDE
"""

import os
import sys
import django

# Configuration Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from myapp.services.scenario_validation_service import ScenarioValidationService
from myapp.services.file_service import FileService


def test_api_validation_multiply():
    """Test de validation via le service utilisé par l'API"""
    print("🧪 Test de validation API pour MULTIPLY et DIVIDE")
    
    # Contenu du scénario avec MULTIPLY
    scenario_content = """TRANSFERT {INIT 3x3; EDIT 7 [0,0]; EDIT 7 [0,2]; EDIT 7 [1,0]; EDIT 7 [1,2]; EDIT 7 [2,0]; EDIT 7 [2,1]}
RESIZE 9x9
COPY (COLOR 7 [0,0 2,2])
MULTIPLY 3 [0,0 2,2]
PASTE [0,0]
END"""
    
    # Charger les données de la tâche
    file_service = FileService()
    task_data_result = file_service.get_file_content('training', '007bbfb7.json')
    
    if not task_data_result or not task_data_result.get('success'):
        print("❌ Impossible de charger les données de la tâche")
        return False
    
    print("✅ Données de la tâche chargées")
    
    # Valider le scénario
    validation_service = ScenarioValidationService()
    validation_result = validation_service.validate_optimized_scenario(
        scenario_content, task_data_result['content'], 0
    )
    
    print(f"\n🔍 Résultat de validation:")
    print(f"  - Valid: {validation_result['valid']}")
    print(f"  - Execution success: {validation_result.get('execution_success', 'N/A')}")
    print(f"  - Grid matches: {validation_result.get('grid_matches_expected', 'N/A')}")
    
    if not validation_result['valid']:
        print(f"\n❌ Erreurs de validation:")
        for error in validation_result.get('errors', []):
            print(f"  - Ligne {error.get('line', '?')}: {error.get('error', 'Erreur inconnue')}")
            print(f"    Commande: {error.get('command', 'N/A')}")
    else:
        print("\n✅ Validation réussie!")
    
    return validation_result['valid']


def test_simple_multiply_validation():
    """Test de validation simple pour MULTIPLY"""
    print("\n🧪 Test de validation simple pour MULTIPLY")
    
    validation_service = ScenarioValidationService()
    
    # Test avec une commande MULTIPLY simple
    simple_scenario = "MULTIPLY 3 [0,0 2,2]\nEND"
    
    # Créer des données de tâche minimales
    minimal_task_data = {
        "train": [
            {
                "input": [[0, 0, 0], [0, 0, 0], [0, 0, 0]],
                "output": [[0, 0, 0], [0, 0, 0], [0, 0, 0]]
            }
        ],
        "test": [
            {
                "input": [[0, 0, 0], [0, 0, 0], [0, 0, 0]]
            }
        ]
    }
    
    result = validation_service.validate_optimized_scenario(simple_scenario, minimal_task_data, 0)
    
    print(f"Résultat validation simple: {result['valid']}")
    if not result['valid']:
        print(f"Erreurs: {result.get('errors', [])}")
    
    return result['valid']


if __name__ == "__main__":
    print("=== Test de validation API pour MULTIPLY/DIVIDE ===")
    
    success1 = test_simple_multiply_validation()
    success2 = test_api_validation_multiply()
    
    if success1 and success2:
        print("\n🎉 Tous les tests de validation API sont réussis!")
    else:
        print("\n💥 Certains tests ont échoué")
        print(f"  - Test simple: {'✅' if success1 else '❌'}")
        print(f"  - Test API complet: {'✅' if success2 else '❌'}")
