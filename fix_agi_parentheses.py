#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script pour corriger les parenthèses incorrectes dans les fichiers AGI.
Selon la règle: les parenthèses ne sont utilisées que pour les sélections multiples.

Exemples de corrections:
- COPY ([0,0 2,2]) → COPY [0,0 2,2]
- PASTE ([3,0]) → PASTE [3,0]
- FILL 5 ([1,1]) → FILL 5 [1,1]
"""

import os
import re
from pathlib import Path
from typing import List, Tuple

class AGIParenthesesFixer:
    def __init__(self, arcdata_path: str):
        self.arcdata_path = Path(arcdata_path)
        self.stats = {
            'files_processed': 0,
            'files_modified': 0,
            'corrections': 0,
            'errors': []
        }
        
        # Pattern pour détecter les parenthèses incorrectes autour d'une seule coordonnée
        # Selon la documentation: parenthèses uniquement pour coordonnées multiples
        self.single_coord_pattern = re.compile(
            r'\b(COPY|PASTE|CUT|FILL|CLEAR|EDIT|REPLACE|SURROUND|ROTATE|FLIP|EXTRACT|INSERT|DELETE)'
            r'(\s+[^\(\[]*?)'
            r'\(\s*(\[[0-9,\s]+\])\s*\)',
            re.MULTILINE
        )
        
        # Pattern pour les sélections simples avec parenthèses superflues
        # Ex: ([1,2]) -> [1,2]
        self.simple_selection_pattern = re.compile(
            r'\(\s*(\[[0-9,\s]+\])\s*\)',
            re.MULTILINE
        )
        
    def fix_content(self, content: str) -> Tuple[str, int]:
        """Corrige le contenu d'un fichier AGI"""
        corrections_count = 0
        
        def replace_single_coord(match):
            nonlocal corrections_count
            command = match.group(1)
            params = match.group(2) if match.group(2) else ''
            coord = match.group(3)
            
            # Vérifier qu'il n'y a qu'une seule coordonnée entre crochets
            # Selon la documentation: [] pour coordonnées uniques/rectangles, () pour multiples
            coord_parts = re.findall(r'\[[0-9,\s]+\]', coord)
            if len(coord_parts) == 1:
                # Une seule coordonnée (cellule ou rectangle), supprimer les parenthèses
                corrections_count += 1
                return f"{command}{params}{coord}"
            else:
                # Plusieurs coordonnées séparées, garder les parenthèses
                return match.group(0)
        
        def replace_simple_selection(match):
            nonlocal corrections_count
            coord = match.group(1)
            
            # Vérifier qu'il n'y a qu'une seule coordonnée
            coord_parts = re.findall(r'\[[0-9,\s]+\]', coord)
            if len(coord_parts) == 1:
                # Vérifier que ce n'est pas déjà dans une commande (éviter les doublons)
                # On cherche si c'est précédé d'une commande
                start_pos = match.start()
                line_start = content.rfind('\n', 0, start_pos) + 1
                line_before = content[line_start:start_pos]
                
                # Si la ligne contient déjà une commande, on ne traite pas (déjà géré par l'autre pattern)
                command_keywords = ['COPY', 'PASTE', 'CUT', 'FILL', 'CLEAR', 'EDIT', 'REPLACE', 'SURROUND', 'ROTATE', 'FLIP', 'EXTRACT', 'INSERT', 'DELETE', 'MULTIPLY', 'DIVIDE']
                if any(keyword in line_before for keyword in command_keywords):
                    return match.group(0)
                
                corrections_count += 1
                return coord
            else:
                # Plusieurs coordonnées, on garde les parenthèses
                return match.group(0)
        
        # Appliquer les corrections pour les commandes
        new_content = self.single_coord_pattern.sub(replace_single_coord, content)
        
        # Appliquer les corrections pour les sélections simples
        new_content = self.simple_selection_pattern.sub(replace_simple_selection, new_content)
        
        return new_content, corrections_count
    
    def process_file(self, file_path: Path) -> bool:
        """Traite un fichier AGI individuel"""
        try:
            # Lire le contenu
            with open(file_path, 'r', encoding='utf-8') as f:
                original_content = f.read()
            
            # Appliquer les corrections
            new_content, corrections_count = self.fix_content(original_content)
            
            # Écrire le nouveau contenu si des corrections ont été apportées
            if corrections_count > 0:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(new_content)
                
                print(f"✅ {file_path.relative_to(self.arcdata_path)}: {corrections_count} corrections")
                self.stats['files_modified'] += 1
                self.stats['corrections'] += corrections_count
                return True
            
            return False
            
        except Exception as e:
            error_msg = f"Erreur lors du traitement de {file_path}: {e}"
            print(f"❌ {error_msg}")
            self.stats['errors'].append(error_msg)
            return False
    
    def scan_and_process(self) -> None:
        """Scanne et traite tous les fichiers AGI"""
        print(f"Scan du répertoire arcdata: {self.arcdata_path}")
        print("="*60)
        
        # Traiter les fichiers .agi
        for file_path in self.arcdata_path.rglob('*.agi'):
            if file_path.is_file():
                self.stats['files_processed'] += 1
                self.process_file(file_path)
        
        # Traiter aussi les fichiers .agi.backup
        for file_path in self.arcdata_path.rglob('*.agi.backup'):
            if file_path.is_file():
                self.stats['files_processed'] += 1
                self.process_file(file_path)
    
    def print_summary(self) -> None:
        """Affiche un résumé des corrections"""
        print("\n" + "="*60)
        print("📊 RÉSUMÉ DES CORRECTIONS AGI")
        print("="*60)
        print(f"Fichiers traités: {self.stats['files_processed']}")
        print(f"Fichiers modifiés: {self.stats['files_modified']}")
        print(f"Corrections appliquées: {self.stats['corrections']}")
        print(f"Erreurs: {len(self.stats['errors'])}")
        
        if self.stats['errors']:
            print("\n❌ ERREURS:")
            for error in self.stats['errors']:
                print(f"  - {error}")
        
        if self.stats['corrections'] > 0:
            print(f"\n✅ Corrections terminées avec succès!")
        else:
            print("\nℹ️ Aucune correction nécessaire.")
    
    def run(self) -> bool:
        """Exécute la correction complète"""
        print("🚀 DÉBUT DE LA CORRECTION DES PARENTHÈSES AGI")
        print("="*60)
        
        # Traiter tous les fichiers
        self.scan_and_process()
        
        # Afficher le résumé
        self.print_summary()
        
        return len(self.stats['errors']) == 0

def main():
    """Fonction principale"""
    project_root = r"c:\Users\<USER>\Documents\Projets\arc-puzzle"
    
    # Rechercher les fichiers AGI dans les répertoires spécifiés
    search_dirs = [
        os.path.join(project_root, "arcdata"),
        os.path.join(project_root, "migration_backup", "arcdata")
    ]
    
    # Vérifier quels répertoires existent
    existing_dirs = [dir_path for dir_path in search_dirs if os.path.exists(dir_path)]
    
    if not existing_dirs:
        print(f"❌ Aucun répertoire arcdata trouvé dans: {search_dirs}")
        return False
    
    print(f"🔍 Répertoires trouvés: {existing_dirs}")
    
    # Traiter tous les répertoires trouvés
    overall_success = True
    for arcdata_path in existing_dirs:
        print(f"\n📁 Traitement du répertoire: {arcdata_path}")
        fixer = AGIParenthesesFixer(arcdata_path)
        success = fixer.run()
        if not success:
            overall_success = False
    
    if overall_success:
        print("\n✅ Correction terminée avec succès pour tous les répertoires!")
    else:
        print("\n❌ Erreurs rencontrées lors de la correction.")
    
    return overall_success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)