import * as monaco from 'monaco-editor/esm/vs/editor/editor.api';
// src/components/resolution/automation/CommandEditor.tsx
import React, { useRef, useEffect } from 'react';
import Editor, { BeforeMount } from '@monaco-editor/react';
import {
  FaPlay,
  FaPause,
  FaStop,
  FaStepForward,
  FaStepBackward,
  FaSave,
  FaFolderOpen,
  FaRecordVinyl
} from 'react-icons/fa';
import { Range } from 'react-range';
import { useAutomationStore } from '../hooks/useAutomationStore';
import { useSelection } from '../hooks/useSelection';
// import { useResolutionStore } from '../hooks/useResolutionStore'; // Remplacé par useGridStateStore
import { useGridStateStore } from '../hooks/useGridStateStore'; // Import du store correct
import { useDisplaySettings } from '../../../hooks/useDisplaySettings';
import { useCommandStore } from '../../../stores/commandStore';
import { useScenarioSync } from '../hooks/useScenarioSync';
import { useUnifiedPlayback } from '../hooks/useUnifiedPlayback';
import { optimizeCommandList, getOptimizationStats } from '../utils/commandOptimizationEngine';
import { commandValidationApi } from '../../../services/api/commandValidationApi';
import { useTask } from '../../../hooks/useTask';
import { useSubset } from '../../../hooks/useSubset';
import { useNotification } from '../../../hooks/useNotification';
import { GridDiffViewer } from './GridDiffViewer';
import './CommandEditor.css';

/**
 * Composant d'éditeur de commandes
 */
export const CommandEditor: React.FC = () => {
  // Récupérer les commandes et l'index de la commande actuelle du store
  const { getCurrentCommands, setCommands, currentCommandIndex, hasLocalChanges, currentScenarioId, playbackSpeed, setPlaybackSpeed, isRecording, startRecording, stopRecording, isOptimizationEnabled, setIsOptimizationEnabled } = useAutomationStore();

  // Hook de sélection pour synchroniser l'état d'enregistrement
  const { setRecordingCommands } = useSelection();

  // Hook pour la synchronisation des scénarios
  const {
    isLoading: scenarioLoading,
    error: scenarioError,
    hasScenario,
    saveLocalChanges,
    loadScenarioForCurrentTask
  } = useScenarioSync();
  
  // Récupérer le thème effectif depuis le contexte
  const { effectiveTheme } = useDisplaySettings();

  // Hook pour accéder aux données de la tâche actuelle
  const { currentTask, currentTestPairIndex } = useTask();

  // Hook pour accéder au subset courant
  const { selectedSubset } = useSubset();
  const { showError } = useNotification();

  // Récupérer les commandes disponibles depuis le store de commandes
  const { isOnline } = useCommandStore();

  // État pour contrôler l'affichage du GridDiffViewer
  const [showGridDiff, setShowGridDiff] = React.useState(false);
  const [validationErrors, setValidationErrors] = React.useState<any[]>([]);


  // Référence à l'éditeur Monaco
  const editorRef = useRef<monaco.editor.IStandaloneCodeEditor | null>(null);

  // Récupérer les commandes du test actuel
  const commands = getCurrentCommands();

  // Convertir les commandes en texte
  const commandsText = commands.join('\n');

  // Fonction appelée avant le montage de l'éditeur pour configurer la coloration syntaxique
  const handleEditorBeforeMount: BeforeMount = (monaco) => {
    // Enregistrer un nouveau langage pour les commandes d'automatisation
    monaco.languages.register({ id: 'arcCommands' });

    // Configuration du tokenizer avec états séparés
    monaco.languages.setMonarchTokensProvider('arcCommands', {
      tokenizer: {
        root: [
          // Commandes avec directions complexes: FLIP HORIZONTAL, ROTATE LEFT, etc.
          [/^(FLIP|ROTATE)\s+(HORIZONTAL|VERTICAL|LEFT|RIGHT)/, 'motifCommand'],
          
          // Commandes de scénario
          [/^(TRANSFERT|MOTIF)\s*[\{\(]/, 'scenarioCommand'],
          
          // Commandes groupées
          [/^(EDITS|FILLS|FLOODFILLS|REPLACES|FLIPS|ROTATES|INSERTS|DELETES|RESIZES)\s*\(/, 'groupedCommand'],
          
          // Commandes presse-papier avec parenthèses (motifs)
          [/^(COPY|CUT|PASTE)\s*\(/, 'motifCommand'],
          
          // Commandes avec cibles
          [/^(INSERT|DELETE)\s+/, 'keyword'],
          
          // Commandes avec format spécial: RESIZE 9x9
          [/^(RESIZE)\s+(\d+x\d+)/, 'keyword'],
          
          // SELECT RELEASE (commande obsolète en deux mots)
          [/^(SELECT)\s+(RELEASE)/, 'obsoleteCommand'],
          
          // Commandes standard
          [/^(CLEAR|FILL|FLOODFILL|SURROUND|REPLACE|EDIT|EXTRACT|RESIZE|SELECT|END|INIT|MULTIPLY|DIVIDE)\b/, 'keyword'],
          
          // Commandes obsolètes - noms seulement
          [/^(PROPOSE|VALIDATE|ROTATE_R|ROTATE_L|FLIP_H|FLIP_V|INSERT_ROW|DELETE_ROW|INSERT_COL|DELETE_COL)\b/, 'obsoleteCommand'],
          
          // Formats obsolètes - noms seulement (pas de parsing des paramètres)
          [/^(SELECT\s+COLOR|FILL\s+\d+,\d+\s+\d+,\d+\s+\d+|CUT\s+\d+,\d+\s+\d+,\d+|COPY\s+\d+,\d+\s+\d+,\d+|PASTE\s+\d+,\d+)\b/, 'obsoleteCommand'],
          
          // Coordonnées dans des crochets
          [/\[[^\]]+\]/, 'coordinate'],
          
          // Coordonnées additionnelles
          [/\[[^\]]+\]/, 'additionalCoordinate'],
          
          // Paramètres génériques
          [/\s+[^\s\[\]]+/, 'parameter'],
          
          // Commentaires
          [/#.*$/, 'comment'],
        ],
      }
    });

    // Thème clair mis à jour
    monaco.editor.defineTheme('arcThemeLight', {
      base: 'vs',
      inherit: true,
      rules: [
        { token: 'keyword', foreground: '0000FF', fontStyle: 'bold' },
        { token: 'motifCommand', foreground: 'FF6B00', fontStyle: 'bold' },
        { token: 'specialSelection', foreground: '8A2BE2', fontStyle: 'bold' },
        { token: 'scenarioCommand', foreground: '800080', fontStyle: 'bold' },
        { token: 'groupedCommand', foreground: '228B22', fontStyle: 'bold' },
        { token: 'obsoleteCommand', foreground: 'CC0000', fontStyle: 'bold strikethrough' },
        { token: 'parameter', foreground: '9932CC' },
        { token: 'direction', foreground: '2E8B57', fontStyle: 'bold' },
        { token: 'target', foreground: '2E8B57' },
        { token: 'coordinate', foreground: 'DD0000' },
        { token: 'additionalCoordinate', foreground: 'FF4500' },
        { token: 'motifCoordinates', foreground: 'FF6B00' },
        { token: 'specialCoordinates', foreground: '8A2BE2' },
        { token: 'scenarioContent', foreground: '800080' },
        { token: 'groupedContent', foreground: '228B22' },
        { token: 'number', foreground: '000088' },
        { token: 'comment', foreground: '008800', fontStyle: 'italic' },
      ],
      colors: {}
    });

    // Thème sombre mis à jour
    monaco.editor.defineTheme('arcThemeDark', {
      base: 'vs-dark',
      inherit: true,
      rules: [
        { token: 'keyword', foreground: '569CD6', fontStyle: 'bold' },
        { token: 'motifCommand', foreground: 'FFA500', fontStyle: 'bold' },
        { token: 'specialSelection', foreground: 'DA70D6', fontStyle: 'bold' },
        { token: 'scenarioCommand', foreground: 'DDA0DD', fontStyle: 'bold' },
        { token: 'groupedCommand', foreground: '4EC9B0', fontStyle: 'bold' },
        { token: 'obsoleteCommand', foreground: 'FF4444', fontStyle: 'bold strikethrough' },
        { token: 'parameter', foreground: 'DDA0DD' },
        { token: 'direction', foreground: '4EC9B0', fontStyle: 'bold' },
        { token: 'target', foreground: '4EC9B0' },
        { token: 'coordinate', foreground: 'CE9178' },
        { token: 'additionalCoordinate', foreground: 'FFB347' },
        { token: 'motifCoordinates', foreground: 'FFA500' },
        { token: 'specialCoordinates', foreground: 'DA70D6' },
        { token: 'scenarioContent', foreground: 'DDA0DD' },
        { token: 'groupedContent', foreground: '4EC9B0' },
        { token: 'number', foreground: 'B5CEA8' },
        { token: 'comment', foreground: '6A9955', fontStyle: 'italic' },
      ],
      colors: {}
    });
  };

  // Fonction appelée lorsque l'éditeur est prêt
  const handleEditorDidMount = (editor: monaco.editor.IStandaloneCodeEditor) => {
    editorRef.current = editor;

    // Configurer l'éditeur avec le thème actuel
    const currentTheme = effectiveTheme === 'dark' ? 'arcThemeDark' : 'arcThemeLight';
    editor.updateOptions({
      readOnly: false,
      minimap: { enabled: false },
      lineNumbers: 'on',  // Activer les numéros de ligne dans l'éditeur
      scrollBeyondLastLine: false,
      wordWrap: 'on',
      wrappingIndent: 'indent',
      automaticLayout: true,
      theme: currentTheme
    });
  };

  // Mettre à jour la surbrillance de la ligne actuelle
  useEffect(() => {
    if (editorRef.current && currentCommandIndex >= 0 && currentCommandIndex < commands.length) {
      try {
        // Ajouter une décoration pour la ligne actuelle
        const lineNumber = currentCommandIndex + 1;
        const model = editorRef.current.getModel();
        
        // Vérifier que le modèle existe et que la ligne est valide
        if (model && lineNumber <= model.getLineCount()) {
          editorRef.current.deltaDecorations(
            [],
            [
              {
                range: {
                  startLineNumber: lineNumber,
                  startColumn: 1,
                  endLineNumber: lineNumber,
                  endColumn: 1,
                },
                options: {
                  isWholeLine: true,
                  className: 'current-command-line',
                },
              },
            ]
          );

          // Faire défiler l'éditeur pour afficher la ligne actuelle
          editorRef.current.revealLineInCenter(lineNumber);
        }
      } catch (error) {
        console.warn('Erreur lors de la mise à jour des décorations:', error);
      }
    }
  }, [currentCommandIndex, commands]);

  // Mettre à jour le thème de l'éditeur lorsque le thème effectif change
  useEffect(() => {
    if (editorRef.current) {
      const newTheme = effectiveTheme === 'dark' ? 'arcThemeDark' : 'arcThemeLight';
      editorRef.current.updateOptions({ theme: newTheme });
    }
  }, [effectiveTheme]);

  // Cleanup pour éviter les fuites mémoire
  useEffect(() => {
    return () => {
      if (editorRef.current) {
        editorRef.current.dispose();
        editorRef.current = null;
      }
    };
  }, []);

  // Synchroniser l'état d'enregistrement avec le hook de sélection
  useEffect(() => {
    setRecordingCommands(isRecording);
  }, [isRecording, setRecordingCommands]);


  // Fonction appelée lorsque le contenu de l'éditeur change
  const handleEditorChange = (value: string | undefined) => {
    if (value !== undefined) {
      // Mettre à jour les commandes dans le store
      const newCommands = value.split('\n').filter(line => line.trim() !== '');
      setCommands(newCommands);
      
      // Note: Pas besoin de forcer la mise à jour de la coloration syntaxique
      // car Monaco gère cela automatiquement. Le setValue() créait une boucle infinie.
    }
  };

  // Fonction pour effacer toutes les commandes
  const handleClearCommands = () => {
    // Récupérer la grille actuelle
    const currentGrid = useGridStateStore.getState().getCurrentGrid(); // Utiliser useGridStateStore
 
    // Réinitialiser les commandes
    setCommands([]);

    // Définir l'état initial avec la grille actuelle
    if (currentGrid) {
      const { setInitialGridState, addUnifiedCommand } = useAutomationStore.getState();
      setInitialGridState(currentGrid.clone());

      // Ajouter la commande d'initialisation (sans numéro de ligne)
      addUnifiedCommand('INIT 3x3');
    }
  };

  // Fonction pour optimiser les commandes
  const handleOptimizeCommands = () => {
    if (!isOptimizationEnabled) {
      return;
    }
    const currentCommands = getCurrentCommands();
    
    // Ne pas optimiser s'il y a moins de 2 commandes
    if (currentCommands.length < 2) {
      return;
    }
    
    // Appliquer l'optimisation
    const optimizedCommands = optimizeCommandList(currentCommands);
    
    // Calculer les statistiques
    const stats = getOptimizationStats(currentCommands, optimizedCommands);
    
    // Mettre à jour les commandes si elles ont été modifiées
    if (stats.optimizationApplied) {
      setCommands(optimizedCommands);
      console.log(`🚀 Optimisation réussie:
        • Lignes de commandes: ${stats.originalLines} → ${stats.optimizedLines} (${stats.originalLines - stats.optimizedLines} lignes supprimées)
        • Commandes originales: ${stats.originalCount}
        • Commandes optimisées: ${stats.optimizedCount}
        • Réduction: ${stats.reduction} commandes (${stats.reductionPercentage}%)
        • Types d'optimisations: ${stats.rulesApplied.join(', ')}
      `);
    } else {
      console.log(`ℹ️ Optimisation: Aucune amélioration possible sur ${stats.originalCount} commandes`);
    }
  };

  // Fonction de validation robuste côté backend
  const handleValidateCommands = async () => {
    const commands = getCurrentCommands();
    if (commands.length <= 1) {
      console.log('⚠️ Validation: Aucune commande à valider');
      return;
    }

    // Vérifier que currentTask est défini avec diagnostic détaillé
    console.log('🔍 Diagnostic currentTask:', {
      currentTask: currentTask,
      hasCurrentTask: !!currentTask,
      currentTaskId: currentTask?.id,
      currentTaskType: typeof currentTask,
      currentTestPairIndex: currentTestPairIndex,
      selectedSubset: selectedSubset
    });
    
    // Diagnostic supplémentaire du hook useTask
    console.log('🔍 Diagnostic useTask complet:', {
      currentTask,
      currentTestPairIndex,
      taskKeys: currentTask ? Object.keys(currentTask) : 'no task',
      taskTests: currentTask?.train?.length || 'no train data'
    });
    
    // Vérifier si une tâche est chargée (avec ou sans ID)
    if (!currentTask || !currentTask.train || !currentTask.test) {
      console.error('❌ Validation impossible: Aucune tâche chargée ou données manquantes');
      console.log('💡 Pour valider des commandes, vous devez d\'abord charger une tâche.');
      console.log('📋 Actions possibles:');
      console.log('   - Charger une tâche aléatoire depuis l\'interface principale');
      console.log('   - Charger une tâche depuis un fichier');
      console.log('   - Sélectionner une tâche depuis la liste des tâches');
      
      // Afficher une notification d'erreur à l'utilisateur
      showError('Aucune tâche valide chargée. Veuillez charger une tâche avec des données d\'entraînement et de test.');
      return;
    }
    
    // Log pour confirmer que la tâche est valide
    console.log('✅ Tâche valide détectée:', {
      hasId: !!currentTask.id,
      trainCount: currentTask.train?.length || 0,
      testCount: currentTask.test?.length || 0
    });

    try {
      console.log('🔍 Démarrage de la validation robuste côté backend...');
      console.log('📋 Commandes à valider:', commands);
      
      // Validation optimisée avec task_id et test_index
      console.log('🎯 Mode validation optimisée: Validation complète avec task_id et test_index');
      
      const validationRequest = {
        scenario_content: commands.join('\n'),
        task_id: currentTask.id || '',
        subset: selectedSubset,
        test_index: currentTestPairIndex
      };
      
      console.log('📤 Requête de validation:', validationRequest);
      console.log('🎯 Validation optimisée complète activée pour la tâche:', currentTask.id);
      
      const result = await commandValidationApi.validateOptimizedScenario(validationRequest);

      if (result) {
        if (result.isValid) {
          console.log('✅ Validation backend réussie:', {
            lignesDeCommande: commands.length,
            errors: result.errors.length,
            warnings: result.warnings.length,
            performance: result.performance ? {
               tempsExecution: `${result.performance.executionTimeMs}ms`,
               lignesDeCommande: result.performance.lineCommandCount,
               vraiNombreCommandes: result.performance.actualCommandCount,
               commandesBackend: result.performance.backendCommandCount,
               ratioOptimisation: result.performance.optimizationRatio,
               optimisationAppliquee: result.performance.optimizationApplied
             } : 'Non disponible'
          });
          
          if (result.warnings.length > 0) {
            console.warn('⚠️ Avertissements détectés:', result.warnings);
          }
          
          if (result.optimizedCommands && result.optimizedCommands.length > 0) {
            console.log('🚀 Optimisations suggérées par le backend disponibles');
          }
        } else {
          console.error('❌ Erreurs de validation détectées:', result.errors);
          
          // Stocker les erreurs pour l'affichage visuel
          setValidationErrors(result.errors);
          
          // Debug: Afficher la structure des erreurs avec grilles formatées
          const formatGridForLog = (grid: number[][]) => {
            if (!grid || !Array.isArray(grid)) return 'Grid non valide';
            return grid.map(row => row.join(' ')).join('\n');
          };
          
          console.log('🔍 Structure des erreurs:');
          result.errors.forEach((error, index) => {
            console.log(`\n--- Erreur ${index + 1} ---`);
            console.log(`Command Index: ${error.commandIndex}`);
            console.log(`Command: ${error.command}`);
            console.log(`Error Type: ${error.errorType}`);
            console.log(`Message: ${error.message}`);
            
            if (error.generated_grid) {
              console.log('\n🔢 Generated Grid:');
              console.log(formatGridForLog(error.generated_grid));
            }
            
            if (error.expected_grid) {
              console.log('\n🎯 Expected Grid:');
              console.log(formatGridForLog(error.expected_grid));
            }
            
            if (error.suggestions && error.suggestions.length > 0) {
              console.log(`\n💡 Suggestions: ${error.suggestions.join(', ')}`);
            }
          });
          
          // Afficher le GridDiffViewer si il y a des erreurs avec des grilles
          const hasGridErrors = result.errors.some(error => {
            console.log('🔍 Vérification erreur:', error);
            console.log('🔍 generated_grid:', error.generated_grid);
            console.log('🔍 expected_grid:', error.expected_grid);
            return error.generated_grid || error.expected_grid;
          });
          
          console.log('🔍 hasGridErrors:', hasGridErrors);
          console.log('🔍 showGridDiff avant:', showGridDiff);
          
          if (hasGridErrors) {
            console.log('✅ Activation du GridDiffViewer');
            setShowGridDiff(true);
          } else {
            console.log('❌ Pas d\'erreurs de grille détectées');
          }
          
          // Afficher les erreurs de manière détaillée
          result.errors.forEach(error => {
            console.error(`Ligne ${error.commandIndex + 1}: ${error.command}
              Type: ${error.errorType}
              Message: ${error.message}
              ${error.suggestions ? 'Suggestions: ' + error.suggestions.join(', ') : ''}`);
          });
        }
      } else {
        console.error('❌ Échec de la communication avec le backend de validation');
      }
    } catch (error) {
      console.error('❌ Erreur lors de la validation backend:', error);
    }
  };

  // Hooks pour les fonctionnalités d'exécution
  const {
    isPlaying,
    currentIndex,
    playCommand,
    pausePlayback,
    stopPlayback,
    stepForward,
    stepBackward,
  } = useUnifiedPlayback();

  // Fonctions d'exécution
  const handlePlayAll = async () => {
    const commands = getCurrentCommands();
    console.log(`[CommandEditor] handlePlayAll - Total commands: ${commands.length}`);
    console.log(`[CommandEditor] Commands array:`, commands);
    
    if (commands.length > 0 && !isPlaying) {
      // S'assurer que l'index commence au début si on n'a pas encore commencé
      const startIndex = Math.max(0, currentIndex);
      console.log(`[CommandEditor] Starting playback from index: ${startIndex}`);
      
      // Lancer une lecture complète commande par commande
      for (let i = startIndex; i < commands.length; i++) {
        const command = commands[i];

        console.log(`[CommandEditor] Executing ${command.split(' ')[0]} ligne de commande ${i+1}`);
        
        // Vérifier que la commande est valide avant de l'exécuter
        if (!command || typeof command !== 'string' || command.trim().length === 0) {
          console.error(`[CommandEditor] Commande invalide à la ligne ${i+1}:`, command);
          break;
        }        
        const success = await playCommand(command);
        if (!success) {
          console.error(`[CommandEditor] Échec de l'exécution de la commande à la ligne ${i+1}: ${command}`);
          break;
        }
      }
    }
  };

  const handleStepForward = async () => {
    const commands = getCurrentCommands();
    if (currentIndex < commands.length) {
      await stepForward(commands[currentIndex]);
    }
  };

  const handleStepBackward = async () => {
    await stepBackward();
  };

  // Fonction pour basculer l'enregistrement
  const toggleRecording = () => {
    if (isRecording) {
      stopRecording();
      console.log('[CommandEditor] Enregistrement arrêté');
    } else {
      startRecording();
      console.log('[CommandEditor] Enregistrement démarré');
    }
  };

  return (
    <div className="command-editor">
      <div className="editor-header">
        <div className="header-left">
          <h3>Commandes :</h3>
          <span className="command-count">
            {commands.length > 0 ? (
              `${currentIndex + 1} / ${commands.length}`
            ) : (
              'Aucune commande'
            )}
          </span>
        </div>
        <div className="editor-actions">
          {/* Indicateurs de statut des scénarios */}
          {scenarioLoading && (
            <div className="scenario-indicator loading" title="Chargement du scénario...">
              🔄 Chargement...
            </div>
          )}
          
          {scenarioError && (
            <div className="scenario-indicator error" title={scenarioError}>
              ⚠️ Erreur
            </div>
          )}
          
          {hasScenario && currentScenarioId && !scenarioLoading && !scenarioError && (
            <div className="scenario-indicator success" title={`Scénario chargé: ${currentScenarioId}`}>
              📋 {currentScenarioId}
            </div>
          )}
          
          {hasLocalChanges && (
            <div className="scenario-indicator modified" title="Modifications non sauvegardées">
              ✏️ Modifié
            </div>
          )}
          
          {!isOnline && (
            <div className="offline-indicator" title="Mode hors ligne - Commandes limitées">
              Hors ligne
            </div>
          )}


          {/* Boutons d'action pour les scénarios */}
          {hasScenario && (
            <>
              <button
                className="scenario-button save-local"
                onClick={saveLocalChanges}
                disabled={!hasLocalChanges || scenarioLoading}
                title="Sauvegarder les modifications localement"
              >
                💾 Sauvegarder
              </button>
              
              <button
                className="scenario-button reload"
                onClick={() => loadScenarioForCurrentTask()}
                disabled={scenarioLoading}
                title="Recharger le scénario depuis le serveur"
              >
                🔄 Recharger
              </button>
            </>
          )}
          
          <button
            className="optimize-commands-button"
            onClick={handleOptimizeCommands}
            disabled={!isOptimizationEnabled || commands.length < 2} // Désactivé si l'optimisation est désactivée ou s'il y a moins de 2 commandes
            title="Optimiser les commandes (regrouper les commandes identiques consécutives)"
          >
            ⚡ Optimiser
          </button>

                    <button
            className={`toggle-optimization-button ${isOptimizationEnabled ? 'enabled' : ''} h-full`}
            onClick={() => setIsOptimizationEnabled(!isOptimizationEnabled)}
            title={isOptimizationEnabled ? "Désactiver l'optimisation" : "Activer l'optimisation"}
          >
            {isOptimizationEnabled ? '⚡ Opti On' : '⚡ Opti Off'}
          </button>
          
          <button
            className="validate-commands-button"
            onClick={handleValidateCommands}
            disabled={commands.length <= 1 || !isOnline || !currentTask} // Désactivé s'il n'y a pas de commandes, si hors ligne ou si aucune tâche chargée
            title={!currentTask ? "Validation impossible: Aucune tâche chargée" : "Validation robuste côté backend (syntaxe, sémantique, exécution)"}
          >
            🔍 Valider
          </button>
          
          <button
            className="clear-commands-button"
            onClick={handleClearCommands}
            disabled={commands.length <= 1} // Désactivé s'il n'y a que la commande INIT ou aucune commande
            title="Effacer toutes les commandes"
          >
            Effacer
          </button>
        </div>
      </div>
      <div className="editor-container">
        <Editor
          height="250px"
          defaultLanguage="arcCommands"
          value={commandsText}
          beforeMount={handleEditorBeforeMount}
          onMount={handleEditorDidMount}
          onChange={handleEditorChange}
          theme={effectiveTheme === 'dark' ? 'arcThemeDark' : 'arcThemeLight'}
          options={{
            readOnly: false,
            minimap: { enabled: false },
            lineNumbers: 'on',  // Activer les numéros de ligne dans l'éditeur
            scrollBeyondLastLine: false,
            wordWrap: 'on',
            wrappingIndent: 'indent',
            automaticLayout: true,
          }}
        />
      </div>
      
      {/* Contrôles de lecture compacts */}
      <div className="playback-controls compact">
        <div className="controls-container-inline">
          <div className="transport-controls">
            {/* Bouton Reculer d'une étape */}
            <button
              className="control-button"
              onClick={handleStepBackward}
              disabled={!commands.length || isPlaying || currentIndex <= 0}
              title="Reculer d'une étape"
            >
              <FaStepBackward />
            </button>

            {/* Bouton Enregistrement */}
            <button
              className={`control-button ${isRecording ? 'recording' : ''}`}
              onClick={toggleRecording}
              disabled={isPlaying}
              title={isRecording ? "Arrêter l'enregistrement" : "Démarrer l'enregistrement"}
            >
              <FaRecordVinyl />
            </button>

            {/* Bouton Lecture/Pause */}
            {isPlaying ? (
              <button
                className="control-button"
                onClick={pausePlayback}
                disabled={!commands.length}
                title="Pause"
              >
                <FaPause />
              </button>
            ) : (
              <button
                className="control-button"
                onClick={handlePlayAll}
                disabled={!commands.length || currentIndex >= commands.length}
                title="Lecture"
              >
                <FaPlay />
              </button>
            )}

            {/* Bouton Stop */}
            <button
              className="control-button"
              onClick={stopPlayback}
              disabled={!commands.length || (!isPlaying && currentIndex === 0)}
              title="Stop"
            >
              <FaStop />
            </button>

            {/* Bouton Avancer d'une étape */}
            <button
              className="control-button"
              onClick={handleStepForward}
              disabled={!commands.length || isPlaying || currentIndex >= commands.length}
              title="Avancer d'une étape"
            >
              <FaStepForward />
            </button>
          </div>

          <div className="speed-control-inline">
            <span className="speed-label">Vitesse :</span>
            <Range
              step={0.1}
              min={0.1}
              max={5}
              values={[playbackSpeed]}
              onChange={(values) => setPlaybackSpeed(values[0])}
              renderTrack={({ props, children }) => (
                <div
                  {...props}
                  className="speed-track"
                >
                  {children}
                </div>
              )}
              renderThumb={({ props }) => {
                // Extraire la clé des props pour l'ajouter séparément
                const { key, ...restProps } = props;
                return (
                  <div
                    key={key}
                    {...restProps}
                    className="speed-thumb"
                  />
                );
              }}
            />
            <span className="speed-value">{playbackSpeed.toFixed(1)}x</span>
          </div>

          <div className="file-controls">
            {/* Bouton Sauvegarder */}
            <button
              className="control-button"
              onClick={saveLocalChanges}
              disabled={!hasLocalChanges || scenarioLoading}
              title="Sauvegarder les modifications localement"
            >
              <FaSave />
            </button>

            {/* Bouton Charger */}
            <button
              className="control-button"
              onClick={() => loadScenarioForCurrentTask()}
              disabled={scenarioLoading}
              title="Recharger le scénario depuis le serveur"
            >
              <FaFolderOpen />
            </button>
          </div>
        </div>
      </div>
      
      {/* Affichage du GridDiffViewer pour les erreurs de validation */}
      {showGridDiff && validationErrors.length > 0 && (
        <GridDiffViewer
          errors={validationErrors}
          onClose={() => setShowGridDiff(false)}
        />
      )}
      
      {/* Debug: Affichage des états */}
      
    </div>
  );
};
