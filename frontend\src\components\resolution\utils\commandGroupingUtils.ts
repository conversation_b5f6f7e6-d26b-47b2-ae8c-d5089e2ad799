import { parseUnifiedCommand, UnifiedCommand } from './commandGenerationUtils';

export interface GroupingRule {
    type: string;
    canGroup: (commands: UnifiedCommand[]) => boolean;
    format: (commands: UnifiedCommand[]) => string;
}

/**
 * Formate une commande groupée selon les règles de regroupement
 */
export function formatGroupedCommand(groupType: string, commands: string[]): string {
    if (!commands || commands.length === 0) {
        return '';
    }
    
    if (commands.length === 1) {
        return commands[0];
    }
    
    // Format: EDITS {EDIT 7 [0,0]; EDIT 8 [0,2]; EDIT 2 [1,0]}
    const commandList = commands.join('; ');
    return `${groupType} {${commandList}}`;
}

/**
 * Parse une commande groupée
 */
export function parseGroupedCommand(command: string): { groupType: string; commands: string[] } | null {
    // Pattern pour commandes groupées: EDITS {command1; command2; command3}
    const groupPattern = /^([A-Z_]+S)\s*\{([^}]+)\}$/;
    const match = command.match(groupPattern);
    
    if (!match) {
        return null;
    }
    
    const groupType = match[1];
    const commandsStr = match[2];
    
    // Diviser les commandes par point-virgule
    const commands = commandsStr.split(';').map(cmd => cmd.trim()).filter(cmd => cmd.length > 0);
    
    return { groupType, commands };
}

/**
 * Formate une commande TRANSFERT
 */
export function formatTransfertCommand(commands: string[]): string {
    if (!commands || commands.length === 0) {
        return 'TRANSFERT {}';
    }
    
    const commandList = commands.join('; ');
    return `TRANSFERT {${commandList}}`;
}

/**
 * Parse une commande TRANSFERT
 */
export function parseTransfertCommand(command: string): string[] | null {
    // Pattern pour TRANSFERT: TRANSFERT {INIT 3x3; EDIT 7 [0,0]; EDIT 9 [0,2]}
    const transfertPattern = /^TRANSFERT\s*\{([^}]+)\}$/;
    const match = command.match(transfertPattern);
    
    if (!match) {
        return null;
    }
    
    const commandsStr = match[1];
    return commandsStr.split(';').map(cmd => cmd.trim()).filter(cmd => cmd.length > 0);
}

/**
 * Détermine si des commandes peuvent être regroupées
 */
export function canGroupCommands(commands: UnifiedCommand[]): boolean {
    if (commands.length < 2) {
        return false;
    }

    const firstAction = commands[0].action;
    const groupableActions = ['EDIT', 'FILL', 'REPLACE', 'CLEAR'];
    const nonGroupableActions = ['MULTIPLY', 'DIVIDE']; // Interdire le regroupement pour ces commandes

    if (!groupableActions.includes(firstAction) || nonGroupableActions.includes(firstAction)) {
        return false;
    }

    // Vérifier que toutes les commandes ont la même action
    return commands.every(cmd => cmd.action === firstAction);
}

/**
 * Regroupe des commandes selon les règles de regroupement
 */
export function groupCommands(commands: string[]): string[] {
    if (commands.length < 2) {
        return commands;
    }
    
    const result: string[] = [];
    let i = 0;
    
    while (i < commands.length) {
        try {
            const current = parseUnifiedCommand(commands[i]);
            const groupableActions = ['EDIT', 'FILL', 'REPLACE', 'CLEAR'];
            const nonGroupableActions = ['MULTIPLY', 'DIVIDE']; // Interdire le regroupement

            if (!groupableActions.includes(current.action) || nonGroupableActions.includes(current.action)) {
                result.push(commands[i]);
                i++;
                continue;
            }
            
            // Collecter les commandes consécutives avec la même action
            const sameActionCommands = [commands[i]];
            const sameActionParsed = [current];
            let j = i + 1;
            
            while (j < commands.length) {
                try {
                    const next = parseUnifiedCommand(commands[j]);
                    if (next.action === current.action) {
                        sameActionCommands.push(commands[j]);
                        sameActionParsed.push(next);
                        j++;
                    } else {
                        break;
                    }
                } catch {
                    break;
                }
            }
            
            // Si on a plus d'une commande, les regrouper
            if (sameActionCommands.length > 1) {
                const grouped = groupSameActionCommands(sameActionCommands, sameActionParsed);
                result.push(grouped);
                i = j;
            } else {
                result.push(commands[i]);
                i++;
            }
            
        } catch {
            result.push(commands[i]);
            i++;
        }
    }
    
    return result;
}

/**
 * Regroupe des commandes avec la même action et éventuellement les mêmes paramètres
 */
function groupSameActionCommands(commands: string[], parsed: UnifiedCommand[]): string {
    const action = parsed[0].action;
    
    // Vérifier si tous les paramètres sont identiques
    const firstParams = parsed[0].parameters;
    const allSameParams = parsed.every(cmd => cmd.parameters === firstParams);
    
    if (allSameParams && firstParams) {
        // Regroupement avec paramètres identiques
        const allCoords: string[] = [];
        for (const cmd of parsed) {
            allCoords.push(...cmd.coordinates, ...cmd.additionalCoordinates);
        }
        
        let result = `${action} ${firstParams}`;
        
        if (allCoords.length > 0) {
            if (allCoords.length === 1) {
                result += ` [${allCoords[0]}]`;
            } else {
                result += ` ([${allCoords.join('] [')}])`;
            }
        }
        
        return result;
    } else {
        // Regroupement avec accolades pour paramètres différents
        const groupName = action + 'S';
        return formatGroupedCommand(groupName, commands);
    }
}

/**
 * Règles de regroupement prédéfinies
 */
export const groupingRules: GroupingRule[] = [
    {
        type: 'EDITS',
        canGroup: (commands: UnifiedCommand[]) => {
            return commands.length > 1 && commands.every(cmd => cmd.action === 'EDIT');
        },
        format: (commands: UnifiedCommand[]) => {
            const commandStrings = commands.map(cmd => {
                let str = cmd.action;
                if (cmd.parameters) str += ` ${cmd.parameters}`;
                if (cmd.coordinates.length > 0) str += ` [${cmd.coordinates.join(' ')}]`;
                return str;
            });
            return formatGroupedCommand('EDITS', commandStrings);
        }
    },
    {
        type: 'FILLS',
        canGroup: (commands: UnifiedCommand[]) => {
            return commands.length > 1 && commands.every(cmd => cmd.action === 'FILL');
        },
        format: (commands: UnifiedCommand[]) => {
            const commandStrings = commands.map(cmd => {
                let str = cmd.action;
                if (cmd.parameters) str += ` ${cmd.parameters}`;
                if (cmd.coordinates.length > 0) str += ` [${cmd.coordinates.join(' ')}]`;
                return str;
            });
            return formatGroupedCommand('FILLS', commandStrings);
        }
    },
    {
        type: 'REPLACES',
        canGroup: (commands: UnifiedCommand[]) => {
            return commands.length > 1 && commands.every(cmd => cmd.action === 'REPLACE');
        },
        format: (commands: UnifiedCommand[]) => {
            const commandStrings = commands.map(cmd => {
                let str = cmd.action;
                if (cmd.parameters) str += ` ${cmd.parameters}`;
                if (cmd.coordinates.length > 0) str += ` [${cmd.coordinates.join(' ')}]`;
                return str;
            });
            return formatGroupedCommand('REPLACES', commandStrings);
        }
    },
    {
        type: 'CLEARS',
        canGroup: (commands: UnifiedCommand[]) => {
            return commands.length > 1 && commands.every(cmd => cmd.action === 'CLEAR');
        },
        format: (commands: UnifiedCommand[]) => {
            const commandStrings = commands.map(cmd => {
                let str = cmd.action;
                if (cmd.coordinates.length > 0) str += ` [${cmd.coordinates.join(' ')}]`;
                return str;
            });
            return formatGroupedCommand('CLEARS', commandStrings);
        }
    }
];

/**
 * Applique une règle de regroupement spécifique
 */
export function applyGroupingRule(commands: UnifiedCommand[], rule: GroupingRule): string | null {
    if (!rule.canGroup(commands)) {
        return null;
    }
    
    try {
        return rule.format(commands);
    } catch (error) {
        console.warn(`[Grouping] Erreur lors de l'application de la règle ${rule.type}: ${error}`);
        return null;
    }
}

/**
 * Trouve la règle de regroupement appropriée pour des commandes
 */
export function findGroupingRule(commands: UnifiedCommand[]): GroupingRule | null {
    return groupingRules.find(rule => rule.canGroup(commands)) || null;
}