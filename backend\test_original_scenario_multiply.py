#!/usr/bin/env python
"""
Test du scénario original qui posait problème
"""

import os
import sys
import django

# Configuration Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from myapp.services.scenario_backend_validation_service import ScenarioBackendValidationService


def test_original_scenario():
    """Test du scénario original qui posait problème"""
    print("🧪 Test du scénario original avec MULTIPLY")
    
    # Scénario exact du frontend qui posait problème
    scenario_content = """TRANSFERT {INIT 3x3; EDIT 7 [0,0]; EDIT 7 [0,2]; EDIT 7 [1,0]; EDIT 7 [1,2]; EDIT 7 [2,0]; EDIT 7 [2,1]}
RESIZE 6x6
COPY (COLOR 7 [0,0 2,2])
MULTIPLY 2 [0,0 2,2]
PASTE [0,0]
END"""
    
    print("📋 Scénario à tester:")
    for i, line in enumerate(scenario_content.split('\n'), 1):
        print(f"  {i}. {line}")
    
    validation_service = ScenarioBackendValidationService()
    
    result = validation_service.validate_scenario(
        'training', '007bbfb7', scenario_content, 0
    )
    
    print(f"\n📊 Résultat de validation:")
    print(f"  - Valid: {result.get('is_valid_by_backend', False)}")
    print(f"  - Command count: {result.get('command_count', 0)}")
    
    if result.get('error_message'):
        print(f"  - Erreur: {result['error_message']}")
        
        # Vérifier si l'erreur est encore "Commande inconnue: MULTIPLY"
        if 'Commande inconnue: MULTIPLY' in result['error_message']:
            print("❌ Le problème de reconnaissance MULTIPLY persiste")
            return False
        else:
            print("✅ MULTIPLY est reconnue (erreur différente)")
            return True
    else:
        print("✅ Scénario validé avec succès")
        return True


def test_multiply_with_clipboard():
    """Test MULTIPLY avec le presse-papier comme dans le scénario original"""
    print("\n🧪 Test MULTIPLY avec presse-papier")
    
    from command_system.command_executor import CommandExecutor
    from command_system.unified_command import UnifiedCommand
    import numpy as np
    
    executor = CommandExecutor()
    
    # Reproduire la séquence exacte du scénario
    commands = [
        "INIT 3x3",
        "EDIT 7 [0,0]",
        "EDIT 7 [0,2]", 
        "EDIT 7 [1,0]",
        "EDIT 7 [1,2]",
        "EDIT 7 [2,0]",
        "EDIT 7 [2,1]",
        "RESIZE 6x6",
        "COPY (COLOR 7 [0,0 2,2])",
        "MULTIPLY 2 [0,0 2,2]",
        "PASTE [0,0]"
    ]
    
    print("Exécution des commandes:")
    for i, command_str in enumerate(commands):
        print(f"{i+1}. {command_str}")
        
        cmd = UnifiedCommand.parse(command_str)
        if not cmd:
            print(f"❌ Erreur de parsing: {command_str}")
            return False
            
        success = executor._execute_unified_command(cmd)
        if not success:
            print(f"❌ Erreur d'exécution: {executor.error}")
            return False
    
    print(f"\n✅ Toutes les commandes exécutées avec succès")
    print(f"Grille finale:")
    print(executor.grid)
    
    # Vérifier que le presse-papier a été modifié par MULTIPLY
    if hasattr(executor, 'clipboard') and executor.clipboard:
        print(f"\nContenu du presse-papier après MULTIPLY:")
        print(f"  - Largeur: {executor.clipboard.get('width', 'N/A')}")
        print(f"  - Hauteur: {executor.clipboard.get('height', 'N/A')}")
        if 'data' in executor.clipboard:
            print(f"  - Données: {len(executor.clipboard['data'])} lignes")
    
    return True


if __name__ == "__main__":
    print("=== Test du scénario original avec MULTIPLY ===")
    
    success1 = test_original_scenario()
    success2 = test_multiply_with_clipboard()
    
    print(f"\n📊 Résumé:")
    print(f"  - Scénario original API: {'✅' if success1 else '❌'}")
    print(f"  - MULTIPLY avec presse-papier: {'✅' if success2 else '❌'}")
    
    if success1 and success2:
        print("\n🎉 Le problème original est résolu!")
        print("✅ MULTIPLY est maintenant reconnue par l'API")
        print("✅ MULTIPLY fonctionne avec le presse-papier")
        print("✅ Le scénario complet s'exécute sans erreur")
    else:
        print("\n💥 Il reste des problèmes à résoudre")
        
        if not success1:
            print("  - Le scénario original ne passe toujours pas l'API")
        if not success2:
            print("  - MULTIPLY ne fonctionne pas correctement avec le presse-papier")
