#!/usr/bin/env python
"""
Test rapide pour vérifier que MULTIPLY et DIVIDE sont reconnus
"""

import os
import sys
import django

# Configuration Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from command_system.unified_command import UnifiedCommand
from command_system.command_executor import CommandExecutor

def test_multiply_divide_recognition():
    """Test que MULTIPLY et DIVIDE sont reconnus"""
    print("🧪 Test de reconnaissance MULTIPLY et DIVIDE")
    
    # Test 1: Parsing des commandes
    print("\n--- Test 1: Parsing ---")
    multiply_cmd = UnifiedCommand.parse("MULTIPLY 3 [0,0 2,2]")
    divide_cmd = UnifiedCommand.parse("DIVIDE 2 [0,0 3,3]")
    
    if multiply_cmd:
        print(f"✅ MULTIPLY parsé: action={multiply_cmd.action}, params={multiply_cmd.parameters}")
        print(f"✅ MULTIPLY validation: {multiply_cmd.validate_syntax()}")
    else:
        print("❌ MULTIPLY non parsé")
        
    if divide_cmd:
        print(f"✅ DIVIDE parsé: action={divide_cmd.action}, params={divide_cmd.parameters}")
        print(f"✅ DIVIDE validation: {divide_cmd.validate_syntax()}")
    else:
        print("❌ DIVIDE non parsé")
    
    # Test 2: Exécution avec CommandExecutor
    print("\n--- Test 2: Exécution ---")
    executor = CommandExecutor()
    
    # Initialiser une grille de test
    import numpy as np
    executor.grid = np.zeros((5, 5), dtype=int)
    executor.height, executor.width = 5, 5
    
    # Test MULTIPLY
    if multiply_cmd:
        try:
            result = executor._execute_unified_command(multiply_cmd)
            print(f"✅ MULTIPLY exécuté: {result}")
            if executor.error:
                print(f"   Erreur: {executor.error}")
        except Exception as e:
            print(f"❌ MULTIPLY erreur: {e}")
    
    # Test DIVIDE
    if divide_cmd:
        try:
            result = executor._execute_unified_command(divide_cmd)
            print(f"✅ DIVIDE exécuté: {result}")
            if executor.error:
                print(f"   Erreur: {executor.error}")
        except Exception as e:
            print(f"❌ DIVIDE erreur: {e}")
    
    return True

if __name__ == "__main__":
    test_multiply_divide_recognition()
