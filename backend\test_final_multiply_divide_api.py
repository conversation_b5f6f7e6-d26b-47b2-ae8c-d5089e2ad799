#!/usr/bin/env python
"""
Test final des commandes MULTIPLY et DIVIDE avec l'API
"""

import os
import sys
import django

# Configuration Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from myapp.services.scenario_backend_validation_service import ScenarioBackendValidationService


def test_multiply_api_recognition():
    """Test que MULTIPLY est maintenant reconnue par l'API"""
    print("🧪 Test de reconnaissance MULTIPLY par l'API")
    
    # Scénario simple avec MULTIPLY
    scenario_content = """INIT 6x6
EDIT 7 [0,0]
EDIT 7 [0,2]
EDIT 7 [1,0]
EDIT 7 [1,2]
EDIT 7 [2,0]
EDIT 7 [2,1]
MULTIPLY 2 [0,0 2,2]
END"""
    
    validation_service = ScenarioBackendValidationService()
    
    result = validation_service.validate_scenario(
        'training', '007bbfb7', scenario_content, 0
    )
    
    print(f"📊 Résultat de validation:")
    print(f"  - Valid: {result.get('is_valid_by_backend', False)}")
    print(f"  - Command count: {result.get('command_count', 0)}")
    
    if result.get('error_message'):
        print(f"  - Erreur: {result['error_message']}")
        
        # Vérifier si l'erreur est liée à la reconnaissance de MULTIPLY
        if 'inconnue' in result['error_message'] and 'MULTIPLY' in result['error_message']:
            print("❌ MULTIPLY n'est toujours pas reconnue")
            return False
        else:
            print("✅ MULTIPLY est reconnue (erreur différente)")
            return True
    else:
        print("✅ MULTIPLY est reconnue et validée")
        return True


def test_divide_api_recognition():
    """Test que DIVIDE est reconnue par l'API"""
    print("\n🧪 Test de reconnaissance DIVIDE par l'API")
    
    # Scénario simple avec DIVIDE
    scenario_content = """INIT 4x4
EDIT 1 [0,0]
EDIT 2 [0,1]
EDIT 3 [1,0]
EDIT 4 [1,1]
EDIT 1 [0,2]
EDIT 2 [0,3]
EDIT 3 [1,2]
EDIT 4 [1,3]
EDIT 1 [2,0]
EDIT 2 [2,1]
EDIT 3 [3,0]
EDIT 4 [3,1]
EDIT 1 [2,2]
EDIT 2 [2,3]
EDIT 3 [3,2]
EDIT 4 [3,3]
DIVIDE 2 [0,0 3,3]
END"""
    
    validation_service = ScenarioBackendValidationService()
    
    result = validation_service.validate_scenario(
        'training', '007bbfb7', scenario_content, 0
    )
    
    print(f"📊 Résultat de validation:")
    print(f"  - Valid: {result.get('is_valid_by_backend', False)}")
    print(f"  - Command count: {result.get('command_count', 0)}")
    
    if result.get('error_message'):
        print(f"  - Erreur: {result['error_message']}")
        
        # Vérifier si l'erreur est liée à la reconnaissance de DIVIDE
        if 'inconnue' in result['error_message'] and 'DIVIDE' in result['error_message']:
            print("❌ DIVIDE n'est toujours pas reconnue")
            return False
        else:
            print("✅ DIVIDE est reconnue (erreur différente)")
            return True
    else:
        print("✅ DIVIDE est reconnue et validée")
        return True


def test_multiply_with_replace_api():
    """Test MULTIPLY avec l'option replace via l'API"""
    print("\n🧪 Test MULTIPLY avec option replace via l'API")
    
    # Scénario avec MULTIPLY et option replace
    scenario_content = """INIT 6x6
EDIT 7 [0,0]
EDIT 8 [0,1]
EDIT 9 [1,0]
EDIT 1 [1,1]
MULTIPLY 2 false [0,0 1,1]
END"""
    
    validation_service = ScenarioBackendValidationService()
    
    result = validation_service.validate_scenario(
        'training', '007bbfb7', scenario_content, 0
    )
    
    print(f"📊 Résultat de validation:")
    print(f"  - Valid: {result.get('is_valid_by_backend', False)}")
    print(f"  - Command count: {result.get('command_count', 0)}")
    
    if result.get('error_message'):
        print(f"  - Erreur: {result['error_message']}")
        
        # Vérifier si l'erreur est liée au paramètre replace
        if 'replace' in result['error_message'].lower():
            print("❌ Problème avec le paramètre replace")
            return False
        else:
            print("✅ Paramètre replace accepté (erreur différente)")
            return True
    else:
        print("✅ MULTIPLY avec replace fonctionne")
        return True


if __name__ == "__main__":
    print("=== Test final API MULTIPLY/DIVIDE ===")
    
    success1 = test_multiply_api_recognition()
    success2 = test_divide_api_recognition()
    success3 = test_multiply_with_replace_api()
    
    print(f"\n📊 Résumé final:")
    print(f"  - MULTIPLY reconnaissance API: {'✅' if success1 else '❌'}")
    print(f"  - DIVIDE reconnaissance API: {'✅' if success2 else '❌'}")
    print(f"  - MULTIPLY avec replace API: {'✅' if success3 else '❌'}")
    
    if success1 and success2 and success3:
        print("\n🎉 Toutes les commandes MULTIPLY/DIVIDE fonctionnent avec l'API!")
        print("✅ Le problème de reconnaissance est résolu")
        print("✅ L'option replace pour MULTIPLY fonctionne")
        print("✅ La validation améliorée pour DIVIDE fonctionne")
    else:
        print("\n💥 Il reste des problèmes à résoudre")
        
        if not success1:
            print("  - MULTIPLY n'est pas reconnue par l'API")
        if not success2:
            print("  - DIVIDE n'est pas reconnue par l'API")
        if not success3:
            print("  - L'option replace de MULTIPLY ne fonctionne pas")
