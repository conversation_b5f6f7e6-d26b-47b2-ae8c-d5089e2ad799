"""
Module de décompression des commandes optimisées frontend.

Ce module permet de décompresser les commandes optimisées générées par le frontend
(comme EDITS {...}, TRANSFERT(...), PROPOSE(...)) en commandes individuelles
que le backend peut valider et exécuter.
"""

import re
import logging
from typing import List, Dict, Any, Optional, Tuple

logger = logging.getLogger(__name__)


class CommandDecompressor:
    """
    Décompresse les commandes optimisées frontend vers format individuel.
    
    Supporte les formats optimisés suivants :
    - EDITS {EDIT 0,0 1; EDIT 0,1 2; ...}
    - TRANSFERT(INIT 3x3;EDIT 0,0 6;...)
    - PROPOSE(GRID 3x3;CELL 0,0 6;...)
    - VALIDATE(RESULT SUCCESS;TEST 0;...)
    """    # Regex pour matcher les blocs optimisés (parenthèses ET accolades, avec espace optionnel)
    OPTIMIZED_BLOCK_REGEX = re.compile(r'^([A-Z_]+)\s*[\(\{](.*?)[\)\}]$')
    
    # Regex pour parser les contenus PROPOSE
    PROPOSE_GRID_REGEX = re.compile(r'GRID\s+(\d+)x(\d+)')
    PROPOSE_CELL_REGEX = re.compile(r'CELL\s+(\d+),(\d+)\s+(\d+)')
    
    def __init__(self):
        """Initialise le décompresseur."""
        self.stats = {
            'total_processed': 0,
            'blocks_decompressed': 0,
            'commands_expanded': 0
        }
    
    def decompress_commands(self, optimized_commands: List[str]) -> List[str]:
        """
        Décompresse une liste de commandes optimisées en commandes individuelles.
        
        Args:
            optimized_commands: Liste des commandes optimisées du frontend
            
        Returns:
            Liste des commandes individuelles décompressées
            
        Examples:
            ['EDITS {EDIT 0,0 1; EDIT 0,1 2}', 'PROPOSE'] 
            -> ['EDIT 0,0 1', 'EDIT 0,1 2', 'PROPOSE']
        """
        self.stats['total_processed'] += len(optimized_commands)
        expanded_commands = []
        
        for command in optimized_commands:
            if not command or not command.strip():
                continue
                
            command = command.strip()
            
            # Vérifier si c'est un bloc optimisé
            block_info = self._parse_optimized_block(command)
            
            if block_info['is_block']:
                # Décompresser le bloc
                decompressed = self._decompress_block(
                    block_info['type'], 
                    block_info['content']
                )
                expanded_commands.extend(decompressed)
                self.stats['blocks_decompressed'] += 1
                self.stats['commands_expanded'] += len(decompressed)
                
                logger.debug(f"Decompressed {block_info['type']} block into {len(decompressed)} commands")
            else:
                # Commande simple, ajouter telle quelle
                expanded_commands.append(command)
        
        logger.info(f"Decompression stats: {self.stats}")
        return expanded_commands
    
    def _parse_optimized_block(self, command: str) -> Dict[str, Any]:
        """
        Parse une commande pour détecter si c'est un bloc optimisé.
        
        Args:
            command: Commande à analyser
            
        Returns:
            Dictionnaire avec les clés :
            - is_block: bool, True si c'est un bloc optimisé
            - type: str, type du bloc (EDITS, TRANSFERT, etc.)
            - content: str, contenu du bloc        """
        logger.debug(f"🔍 Parsing command: {command}")
        logger.debug(f"🔍 Using regex: {self.OPTIMIZED_BLOCK_REGEX.pattern}")
        
        match = self.OPTIMIZED_BLOCK_REGEX.match(command)
        logger.debug(f"🔍 Regex match result: {match}")
        
        if match:
            block_type = match.group(1)
            content = match.group(2)
            
            logger.debug(f"✅ Block detected - Type: {block_type}, Content: {content}")
            
            return {
                'is_block': True,
                'type': block_type,
                'content': content
            }
        
        logger.debug(f"❌ No block detected, treating as simple command")
        return {
            'is_block': False,
            'type': None,
            'content': None
        }
    
    def _decompress_block(self, block_type: str, content: str) -> List[str]:
        """
        Décompresse un bloc selon son type.
        
        Args:
            block_type: Type du bloc (EDITS, TRANSFERT, etc.)
            content: Contenu du bloc
            
        Returns:
            Liste des commandes décompressées
        """
        try:
            if block_type == 'EDITS':
                return self._decompress_edits_block(content)
            elif block_type == 'TRANSFERT':
                return self._decompress_transfert_block(content)
            # PROPOSE et VALIDATE sont obsolètes et ne doivent pas être supportés
            elif block_type == 'SELECTS':
                return self._decompress_selects_block(content)
            elif block_type == 'FILLS':
                return self._decompress_fills_block(content)
            elif block_type == 'FLOODFILLS':
                return self._decompress_floodfills_block(content)
            elif block_type == 'REPLACES':
                return self._decompress_replaces_block(content)
            elif block_type == 'FLIPS':
                return self._decompress_flips_block(content)
            elif block_type == 'ROTATES':
                return self._decompress_rotates_block(content)
            elif block_type == 'INSERTS':
                return self._decompress_inserts_block(content)
            elif block_type == 'DELETES':
                return self._decompress_deletes_block(content)
            # Types obsolètes et interdits supprimés:
            # - RESIZES (interdit)
            # - DELETE_ROWS, DELETE_COLS (obsolètes) 
            # - INSERT_ROW, INSERT_COL (obsolètes)
            # - SURROUND_SELECTIONS (n'existe pas, doit être SURROUNDS)
            elif block_type == 'PASTES':
                return self._decompress_pastes_block(content)
            # Nouveaux blocs pour commandes unifiées valides
            elif block_type == 'CLEARS':
                return self._decompress_clears_block(content)
            elif block_type == 'SURROUNDS':
                return self._decompress_surrounds_block(content)
            # SPLITS, SELECTIONS n'existent pas - supprimés
            elif block_type == 'EXTRACTS':
                return self._decompress_extracts_block(content)
            elif block_type == 'FLIP':
                return self._decompress_flip_block(content)            # SELECT_INVERT, SELECT_COLOR en format individuel seulement
            # Les versions groupées (SELECT_INVERTS, SELECT_COLORS, SELECT_RELEASES) sont INTERDITES
            elif block_type == 'SELECT_INVERT':
                return self._decompress_select_invert_block(content)
            elif block_type == 'SELECT_COLOR':
                return self._decompress_select_color_block(content)
            elif block_type == 'EXTRACT':
                return self._decompress_extract_block(content)
            else:
                # Blocs obsolètes/interdits explicitement rejetés
                obsolete_blocks = [
                    'PROPOSE', 'VALIDATE', 'DELETE_ROWS', 'DELETE_COLS', 
                    'INSERT_ROW', 'INSERT_COL', 'RESIZES', 'INITS',
                    'SELECT_INVERTS', 'SELECT_RELEASES', 'SELECT_COLORS',
                    'SPLIT', 'SPLITS', 'SELECTIONS', 'SURROUND_SELECTIONS',
                    'EXTRACTS', 'FLIP', 'SELECT_INVERT', 'SELECT_COLOR',
                    'SELECTS', 'EXTRACTS'
                ]
                
                if block_type in obsolete_blocks:
                    logger.warning(f"Obsolete/forbidden block type rejected: {block_type}")
                    return []  # Rejeter explicitement en retournant une liste vide
                else:
                    logger.warning(f"Unknown block type: {block_type}")
                    return [f"{block_type} ({content})"]  # Garder l'espace comme l'original
                
        except Exception as e:
            logger.error(f"Error decompressing {block_type} block: {e}")
            return [f"{block_type}({content})"]  # Retourner tel quel en cas d'erreur
    
    def _decompress_edits_block(self, content: str) -> List[str]:
        """
        Décompresse un bloc EDITS.
        
        Args:
            content: Contenu du bloc EDITS
            
        Returns:
            Liste des commandes EDIT individuelles
            
        Example:
            "EDIT 0,0 1; EDIT 0,1 2; EDIT 0,2 3"
            -> ["EDIT 0,0 1", "EDIT 0,1 2", "EDIT 0,2 3"]
        """
        if not content:
            return []
        
        # Séparer par ; et nettoyer
        commands = [cmd.strip() for cmd in content.split(';') if cmd.strip()]
        
        # Vérifier que toutes les commandes sont bien des EDIT
        valid_commands = []
        for cmd in commands:
            if cmd.startswith('EDIT '):
                valid_commands.append(cmd)
            else:
                logger.warning(f"Invalid EDIT command in block: {cmd}")
        
        return valid_commands
    
    def _decompress_transfert_block(self, content: str) -> List[str]:
        """
        Décompresse un bloc TRANSFERT.
        
        Args:
            content: Contenu du bloc TRANSFERT
            
        Returns:
            Liste des commandes individuelles
            
        Example:
            "INIT 3x3;EDIT 0,0 6;EDIT 0,1 3"
            -> ["INIT 3x3", "EDIT 0,0 6", "EDIT 0,1 3"]
        """
        if not content:
            return []
        
        # Séparer par ; et nettoyer
        commands = [cmd.strip() for cmd in content.split(';') if cmd.strip()]
          # Valider les commandes
        valid_commands = []
        for cmd in commands:
            # Accepter toutes les commandes valides ARC
            if cmd.startswith(('INIT ', 'COPY ', 'EDIT ', 'FILL ', 'MOVE ', 'REPLACE ', 'PROPOSE')):
                valid_commands.append(cmd)
            else:
                logger.warning(f"Invalid TRANSFERT command in block: {cmd}")
        
        return valid_commands
    
    def _decompress_propose_block(self, content: str) -> List[str]:
        """
        Décompresse un bloc PROPOSE en reconstruisant les commandes.
        
        Args:
            content: Contenu du bloc PROPOSE
            
        Returns:
            Liste des commandes reconstruites
            
        Example:
            "GRID 3x3;CELL 0,0 6;CELL 1,1 8"
            -> ["INIT 3x3", "EDIT 0,0 6", "EDIT 1,1 8", "PROPOSE"]
        """
        if not content:
            return ["PROPOSE"]  # PROPOSE vide
        
        commands = []
        
        # Parser le contenu par éléments séparés par ;
        elements = [elem.strip() for elem in content.split(';') if elem.strip()]
        
        grid_initialized = False
        
        for element in elements:
            # Vérifier si c'est une définition de grille
            grid_match = self.PROPOSE_GRID_REGEX.match(element)
            if grid_match:
                width = grid_match.group(1)
                height = grid_match.group(2)
                commands.append(f"INIT {width}x{height}")
                grid_initialized = True
                continue
            
            # Vérifier si c'est une cellule
            cell_match = self.PROPOSE_CELL_REGEX.match(element)
            if cell_match:
                row = cell_match.group(1)
                col = cell_match.group(2)
                value = cell_match.group(3)
                commands.append(f"EDIT {row},{col} {value}")
                continue
            
            # Autres éléments non reconnus
            logger.warning(f"Unknown PROPOSE element: {element}")
        
        # Ajouter la commande PROPOSE finale
        commands.append("PROPOSE")
        
        return commands
    
    def _decompress_validate_block(self, content: str) -> List[str]:
        """
        Décompresse un bloc VALIDATE.
        
        Args:
            content: Contenu du bloc VALIDATE
            
        Returns:
            Liste des commandes (généralement juste VALIDATE)
            
        Note:
            Les blocs VALIDATE contiennent généralement des métadonnées
            plutôt que des commandes à exécuter. On retourne juste VALIDATE.
        """
        # Pour l'instant, on simplifie en retournant juste VALIDATE
        # Le contenu est principalement informatif
        return ["VALIDATE"]
    
    def _decompress_selects_block(self, content: str) -> List[str]:
        """
        Décompresse un bloc SELECTS.
        
        Args:
            content: Contenu du bloc SELECTS
            
        Returns:
            Liste des commandes SELECT individuelles
            
        Example:
            "SELECT 0,0 1,1; SELECT 2,2 3,3"
            -> ["SELECT 0,0 1,1", "SELECT 2,2 3,3"]
        """
        if not content:
            return []
        
        # Séparer par ; et nettoyer
        commands = [cmd.strip() for cmd in content.split(';') if cmd.strip()]
        
        # Vérifier que toutes les commandes sont bien des SELECT
        valid_commands = []
        for cmd in commands:
            if cmd.startswith('SELECT '):
                valid_commands.append(cmd)
            else:
                logger.warning(f"Invalid SELECT command in block: {cmd}")
        
        return valid_commands
    
    def _decompress_fills_block(self, content: str) -> List[str]:
        """
        Décompresse un bloc FILLS.
        
        Args:
            content: Contenu du bloc FILLS
            
        Returns:
            Liste des commandes FILL individuelles
        """
        if not content:
            return []
        
        commands = [cmd.strip() for cmd in content.split(';') if cmd.strip()]
        
        valid_commands = []
        for cmd in commands:
            if cmd.startswith('FILL '):
                valid_commands.append(cmd)
            else:
                logger.warning(f"Invalid FILL command in block: {cmd}")
        
        return valid_commands
    
    def _decompress_floodfills_block(self, content: str) -> List[str]:
        """
        Décompresse un bloc FLOODFILLS.
        
        Args:
            content: Contenu du bloc FLOODFILLS
            
        Returns:
            Liste des commandes FLOODFILL individuelles
        """
        if not content:
            return []
        
        commands = [cmd.strip() for cmd in content.split(';') if cmd.strip()]
        
        valid_commands = []
        for cmd in commands:
            if cmd.startswith('FLOODFILL '):
                valid_commands.append(cmd)
            else:
                logger.warning(f"Invalid FLOODFILL command in block: {cmd}")
        
        return valid_commands
    
    def _decompress_replaces_block(self, content: str) -> List[str]:
        """
        Décompresse un bloc REPLACES.
        
        Args:
            content: Contenu du bloc REPLACES
            
        Returns:
            Liste des commandes REPLACE individuelles
        """
        if not content:
            return []
        
        commands = [cmd.strip() for cmd in content.split(';') if cmd.strip()]
        
        valid_commands = []
        for cmd in commands:
            if cmd.startswith('REPLACE '):
                valid_commands.append(cmd)
            else:
                logger.warning(f"Invalid REPLACE command in block: {cmd}")
        
        return valid_commands
    
    def _decompress_flips_block(self, content: str) -> List[str]:
        """
        Décompresse un bloc FLIPS.
        
        Args:
            content: Contenu du bloc FLIPS
            
        Returns:
            Liste des commandes FLIP individuelles
        """
        if not content:
            return []
        
        commands = [cmd.strip() for cmd in content.split(';') if cmd.strip()]
        
        valid_commands = []
        for cmd in commands:
            if cmd.startswith('FLIP '):
                valid_commands.append(cmd)
            else:
                logger.warning(f"Invalid FLIP command in block: {cmd}")
        
        return valid_commands
    
    def _decompress_rotates_block(self, content: str) -> List[str]:
        """
        Décompresse un bloc ROTATES.
        
        Args:
            content: Contenu du bloc ROTATES
            
        Returns:
            Liste des commandes ROTATE individuelles
        """
        if not content:
            return []
        
        commands = [cmd.strip() for cmd in content.split(';') if cmd.strip()]
        
        valid_commands = []
        for cmd in commands:
            if cmd.startswith('ROTATE '):
                valid_commands.append(cmd)
            else:
                logger.warning(f"Invalid ROTATE command in block: {cmd}")
        
        return valid_commands
    
    def _decompress_inserts_block(self, content: str) -> List[str]:
        """
        Décompresse un bloc INSERTS.
        
        Args:
            content: Contenu du bloc INSERTS
            
        Returns:
            Liste des commandes INSERT individuelles
        """
        if not content:
            return []
        
        commands = [cmd.strip() for cmd in content.split(';') if cmd.strip()]
        
        valid_commands = []
        for cmd in commands:
            if cmd.startswith('INSERT '):
                valid_commands.append(cmd)
            else:
                logger.warning(f"Invalid INSERT command in block: {cmd}")
        
        return valid_commands
    
    def _decompress_deletes_block(self, content: str) -> List[str]:
        """
        Décompresse un bloc DELETES.
        
        Args:
            content: Contenu du bloc DELETES
            
        Returns:
            Liste des commandes DELETE individuelles
        """
        if not content:
            return []
        
        commands = [cmd.strip() for cmd in content.split(';') if cmd.strip()]
        
        valid_commands = []
        for cmd in commands:
            if cmd.startswith('DELETE '):
                valid_commands.append(cmd)
            else:
                logger.warning(f"Invalid DELETE command in block: {cmd}")
        
        return valid_commands
    
    def _decompress_resizes_block(self, content: str) -> List[str]:
        """
        Décompresse un bloc RESIZES.
        
        Args:
            content: Contenu du bloc RESIZES
            
        Returns:
            Liste des commandes RESIZE individuelles
        """
        if not content:
            return []
        
        commands = [cmd.strip() for cmd in content.split(';') if cmd.strip()]
        
        valid_commands = []
        for cmd in commands:
            if cmd.startswith('RESIZE '):
                valid_commands.append(cmd)
            else:
                logger.warning(f"Invalid RESIZE command in block: {cmd}")
        
        return valid_commands
    
    def _decompress_delete_rows_block(self, content: str) -> List[str]:
        """
        Décompresse un bloc DELETE_ROWS.
        
        Args:
            content: Contenu du bloc DELETE_ROWS
            
        Returns:
            Liste des commandes DELETE ROW individuelles
        """
        if not content:
            return []
        
        commands = [cmd.strip() for cmd in content.split(';') if cmd.strip()]
        
        valid_commands = []
        for cmd in commands:
            if cmd.startswith('DELETE_ROW '):
                # Convertir DELETE_ROW -> DELETE ROW
                new_cmd = cmd.replace('DELETE_ROW ', 'DELETE ROW ', 1)
                valid_commands.append(new_cmd)
            elif cmd.startswith('DELETE ROW '):
                valid_commands.append(cmd)
            else:
                logger.warning(f"Invalid DELETE_ROW command in block: {cmd}")
        
        return valid_commands
    
    def _decompress_delete_cols_block(self, content: str) -> List[str]:
        """
        Décompresse un bloc DELETE_COLS.
        
        Args:
            content: Contenu du bloc DELETE_COLS
            
        Returns:
            Liste des commandes DELETE COL individuelles
        """
        if not content:
            return []
        
        commands = [cmd.strip() for cmd in content.split(';') if cmd.strip()]
        
        valid_commands = []
        for cmd in commands:
            if cmd.startswith('DELETE_COL '):
                # Convertir DELETE_COL -> DELETE COL
                new_cmd = cmd.replace('DELETE_COL ', 'DELETE COL ', 1)
                valid_commands.append(new_cmd)
            elif cmd.startswith('DELETE COL '):
                valid_commands.append(cmd)
            else:
                logger.warning(f"Invalid DELETE_COL command in block: {cmd}")
        
        return valid_commands
    
    def _decompress_surround_selections_block(self, content: str) -> List[str]:
        """
        Décompresse un bloc SURROUND_SELECTIONS.
        
        Args:
            content: Contenu du bloc SURROUND_SELECTIONS
            
        Returns:
            Liste des commandes SURROUND_SELECTION individuelles
        """
        if not content:
            return []
        
        commands = [cmd.strip() for cmd in content.split(';') if cmd.strip()]
        
        valid_commands = []
        for cmd in commands:
            if cmd.startswith('SURROUND_SELECTION '):
                valid_commands.append(cmd)
            else:
                logger.warning(f"Invalid SURROUND_SELECTION command in block: {cmd}")
        
        return valid_commands
    
    def _decompress_pastes_block(self, content: str) -> List[str]:
        """Handle individual PASTES command (not a group)"""
        self.stats['commands_expanded'] += 1
        return [f"PASTES{content}"]
    
    def _decompress_clears_block(self, content: str) -> List[str]:
        """
        Décompresse un bloc CLEARS.
        
        Args:
            content: Contenu du bloc CLEARS
            
        Returns:
            Liste des commandes CLEAR individuelles
        """
        if not content:
            return []
        
        commands = [cmd.strip() for cmd in content.split(';') if cmd.strip()]
        
        valid_commands = []
        for cmd in commands:
            if cmd.startswith('CLEAR '):
                valid_commands.append(cmd)
            else:
                logger.warning(f"Invalid CLEAR command in block: {cmd}")
        
        return valid_commands

    def _decompress_surrounds_block(self, content: str) -> List[str]:
        """
        Décompresse un bloc SURROUNDS.
        
        Args:
            content: Contenu du bloc SURROUNDS
            
        Returns:
            Liste des commandes SURROUND individuelles
        """
        if not content:
            return []
        
        commands = [cmd.strip() for cmd in content.split(';') if cmd.strip()]
        
        valid_commands = []
        for cmd in commands:
            if cmd.startswith('SURROUND '):
                valid_commands.append(cmd)
            else:
                logger.warning(f"Invalid SURROUND command in block: {cmd}")
        
        return valid_commands

    def _decompress_extracts_block(self, content: str) -> List[str]:
        """
        Décompresse un bloc EXTRACTS.
        
        Args:
            content: Contenu du bloc EXTRACTS
            
        Returns:
            Liste des commandes EXTRACT individuelles
        """
        if not content:
            return []
        
        commands = [cmd.strip() for cmd in content.split(';') if cmd.strip()]
        
        valid_commands = []
        for cmd in commands:
            if cmd.startswith('EXTRACT '):
                valid_commands.append(cmd)
            else:
                logger.warning(f"Invalid EXTRACT command in block: {cmd}")
        
        return valid_commands
    
    def _decompress_flip_block(self, content: str) -> List[str]:
        """
        Décompresse un bloc FLIP.
        
        Args:
            content: Contenu du bloc FLIP, ex: "HORIZONTAL([0,0 1,1])"
            
        Returns:
            Liste avec la commande FLIP complète.
        """
        if not content:
            return []
        
        return [f"FLIP {content}"]

    def _decompress_select_invert_block(self, content):
        """Handle individual SELECT_INVERT command (not a group)"""
        self.stats['commands_expanded'] += 1
        return [f"SELECT_INVERT{content}"]
    
    def _decompress_select_color_block(self, content):
        """Handle individual SELECT_COLOR command (not a group)"""
        self.stats['commands_expanded'] += 1
        return [f"SELECT_COLOR{content}"]

    def get_stats(self) -> Dict[str, int]:
        """
        Retourne les statistiques de décompression.
        
        Returns:
            Dictionnaire avec les statistiques
        """
        return self.stats.copy()
    
    def reset_stats(self) -> None:
        """Remet à zéro les statistiques."""
        self.stats = {
            'total_processed': 0,
            'blocks_decompressed': 0,
            'commands_expanded': 0
        }


def decompress_scenario_commands(scenario_content: str) -> List[str]:
    """
    Fonction utilitaire pour décompresser le contenu d'un scénario.
    
    Args:
        scenario_content: Contenu du scénario (lignes séparées par \n)
        
    Returns:
        Liste des commandes décompressées
    """
    if not scenario_content:
        return []
    
    # Séparer par lignes et nettoyer
    lines = [line.strip() for line in scenario_content.split('\n') if line.strip()]
    
    # Décompresser
    decompressor = CommandDecompressor()
    return decompressor.decompress_commands(lines)