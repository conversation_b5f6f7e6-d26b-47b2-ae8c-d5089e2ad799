#!/usr/bin/env python
"""
Test de validation des commandes MULTIPLY et DIVIDE avec rechargement forcé
"""

import os
import sys
import django

# Configuration Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from myapp.services.unified_command_validation_service import UnifiedCommandValidationService


def test_multiply_divide_validation_fix():
    """Test que les commandes MULTIPLY et DIVIDE sont reconnues après rechargement"""
    print("🧪 Test de validation MULTIPLY et DIVIDE avec rechargement forcé")
    
    # Créer le service de validation
    validation_service = UnifiedCommandValidationService()
    
    print(f"\n🔍 Commandes chargées initialement: {list(validation_service.unified_commands.keys())}")
    
    # Forcer le rechargement
    print("\n🔄 Rechargement forcé des commandes...")
    validation_service.reload_commands()
    
    print(f"🔍 Commandes après rechargement: {list(validation_service.unified_commands.keys())}")
    
    # Vérifier que MULTIPLY et DIVIDE sont présentes
    multiply_present = 'MULTIPLY' in validation_service.unified_commands
    divide_present = 'DIVIDE' in validation_service.unified_commands
    
    print(f"\n✅ MULTIPLY présente: {multiply_present}")
    print(f"✅ DIVIDE présente: {divide_present}")
    
    if multiply_present and divide_present:
        print("\n🎉 Les commandes MULTIPLY et DIVIDE sont bien reconnues!")
        
        # Tester la validation
        test_commands = [
            "MULTIPLY 3 [0,0 2,2]",
            "DIVIDE 2 [0,0 3,3]"
        ]
        
        for command in test_commands:
            print(f"\n--- Test validation: {command} ---")
            result = validation_service.validate_command(command)
            print(f"Résultat: {result['valid']}")
            if not result['valid']:
                print(f"Erreurs: {result['errors']}")
            else:
                print(f"Format: {result['format_type']}")
                print(f"Commande: {result['command_name']}")
        
        return True
    else:
        print("\n❌ Les commandes MULTIPLY et DIVIDE ne sont pas reconnues")
        return False


if __name__ == "__main__":
    success = test_multiply_divide_validation_fix()
    if success:
        print("\n🎯 Test réussi!")
    else:
        print("\n💥 Test échoué!")
