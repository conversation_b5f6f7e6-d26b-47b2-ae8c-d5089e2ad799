// src/components/resolution/Toolbar/NewModificationsToolbar.tsx
import React, { useState } from 'react';
import { useSelection } from '../hooks';
import { useGridStateStore } from '../hooks/useGridStateStore';
import { useAutomationStore } from '../hooks/useAutomationStore';
import { deleteRows, deleteColumns, insertRow, insertColumn } from '../utils/gridOperations';
import { Grid } from '../../../lib/grid';
import toolbarStyles from './Toolbar.module.css';
import dialogStyles from '../Dialogs/Dialog.module.css';

/**
 * Interface pour le dialogue d'ajout
 */
interface AddDialogConfig {
  type: 'row' | 'column';
  position: 'before' | 'after' | 'left' | 'right';
  count: number;
}

/**
 * Composant de dialogue pour ajouter des lignes/colonnes
 */
const AddDialog: React.FC<{
  onClose: () => void;
  onValidate: (config: AddDialogConfig) => void;
  initialType: 'row' | 'column';
}> = ({ onClose, onValidate, initialType }) => {
  const [type, setType] = useState<'row' | 'column'>(initialType);
  const [position, setPosition] = useState<'before' | 'after' | 'left' | 'right'>(
    initialType === 'row' ? 'before' : 'left'
  );
  const [count, setCount] = useState(1);

  const handleValidate = () => {
    if (count > 0) {
      onValidate({ type, position, count });
      onClose();
    }
  };

  return (
    <div className={dialogStyles.dialogOverlay} onClick={onClose}>
      <div className={dialogStyles.dialogContent} onClick={(e) => e.stopPropagation()}>
        <h3>Ajouter des lignes/colonnes</h3>
        
        <div className={dialogStyles.formGroup}>
          <label className={dialogStyles.formLabel}>Type :</label>
          <div className={dialogStyles.radioGroup}>
            <label className={dialogStyles.radioOption}>
              <input
                type="radio"
                value="row"
                checked={type === 'row'}
                onChange={(e) => {
                  setType(e.target.value as 'row');
                  setPosition('before');
                }}
              />
              <span>Ligne(s)</span>
            </label>
            <label className={dialogStyles.radioOption}>
              <input
                type="radio"
                value="column"
                checked={type === 'column'}
                onChange={(e) => {
                  setType(e.target.value as 'column');
                  setPosition('left');
                }}
              />
              <span>Colonne(s)</span>
            </label>
          </div>
        </div>

        <div className={dialogStyles.formGroup}>
          <label className={dialogStyles.formLabel}>Position :</label>
          <div className={dialogStyles.radioGroup}>
            {type === 'row' ? (
              <>
                <label className={dialogStyles.radioOption}>
                  <input
                    type="radio"
                    value="before"
                    checked={position === 'before'}
                    onChange={(e) => setPosition(e.target.value as 'before')}
                  />
                  <span>Au-dessus</span>
                </label>
                <label className={dialogStyles.radioOption}>
                  <input
                    type="radio"
                    value="after"
                    checked={position === 'after'}
                    onChange={(e) => setPosition(e.target.value as 'after')}
                  />
                  <span>En-dessous</span>
                </label>
              </>
            ) : (
              <>
                <label className={dialogStyles.radioOption}>
                  <input
                    type="radio"
                    value="left"
                    checked={position === 'left'}
                    onChange={(e) => setPosition(e.target.value as 'left')}
                  />
                  <span>À gauche</span>
                </label>
                <label className={dialogStyles.radioOption}>
                  <input
                    type="radio"
                    value="right"
                    checked={position === 'right'}
                    onChange={(e) => setPosition(e.target.value as 'right')}
                  />
                  <span>À droite</span>
                </label>
              </>
            )}
          </div>
        </div>

        <div className={dialogStyles.formGroup}>
          <label className={dialogStyles.formLabel}>Nombre (1-10) :</label>
          <input
            type="number"
            min="1"
            max="10"
            value={count}
            onChange={(e) => setCount(Math.max(1, Math.min(10, parseInt(e.target.value) || 1)))}
            className={dialogStyles.numberInput}
          />
        </div>

        <div className={dialogStyles.dialogButtons}>
          <button onClick={handleValidate}>
            Valider
          </button>
          <button onClick={onClose}>
            Annuler
          </button>
        </div>
      </div>
    </div>
  );
};

/**
 * ÉTAPE 3 - NOUVELLE BARRE MODIFICATIONS
 * Contient : Ajouter (avec dialogue) + Supprimer (automatique)
 */
export const NewModificationsToolbar: React.FC = () => {
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [addDialogInitialType, setAddDialogInitialType] = useState<'row' | 'column'>('row');

  const { selectedCells, getSelectionBounds } = useSelection();
  const { getCurrentGrid, replaceCurrentGrid } = useGridStateStore();

  // Vérifier s'il y a une sélection
  const hasSelection = selectedCells.size > 0;

  // Gestionnaire pour ouvrir le dialogue d'ajout
  const handleAdd = () => {
    // Déterminer le type initial basé sur la sélection actuelle
    const bounds = getSelectionBounds();
    if (bounds) {
      const rowSpan = bounds.endRow - bounds.startRow + 1;
      const colSpan = bounds.endCol - bounds.startCol + 1;
      // Si la sélection est plus large que haute, proposer d'ajouter des colonnes
      setAddDialogInitialType(colSpan > rowSpan ? 'column' : 'row');
    }
    setShowAddDialog(true);
  };

  // Gestionnaire pour valider l'ajout
  const handleValidateAdd = (config: AddDialogConfig) => {
    const bounds = getSelectionBounds();
    if (!bounds) return;

    const { type, position, count } = config;

    try {
      if (type === 'row') {
        const insertPosition = position === 'before' ? bounds.startRow : bounds.endRow + 1;
        for (let i = 0; i < count; i++) {
          insertRow(insertPosition + i);
        }
        
        // Ajouter commande d'automatisation
        //const automationStore = useAutomationStore.getState();
        //automationStore.addCommand('INSERT ROWS', `${insertPosition} ${count} ${position}`);
        applyActionToSelection('INSERT ROWS');
      } else {
        const insertPosition = position === 'left' ? bounds.startCol : bounds.endCol + 1;
        for (let i = 0; i < count; i++) {
          insertColumn(insertPosition + i);
        }
        
        // Ajouter commande d'automatisation
        //const automationStore = useAutomationStore.getState();
        //automationStore.addCommand('INSERT COLUMNS', `${insertPosition} ${count} ${position}`);
        applyActionToSelection('INSERT COLUMNS');
      }
    } catch (error) {
      console.error('[NewModificationsToolbar] Failed to add rows/columns:', error);
    }
  };

  // Gestionnaire pour supprimer automatiquement
  const handleAutoDelete = () => {
    const bounds = getSelectionBounds();
    if (!bounds) return;

    const grid = getCurrentGrid();
    if (!grid) return;

    // Analyser la sélection pour déterminer quoi supprimer
    const rowSpan = bounds.endRow - bounds.startRow + 1;
    const colSpan = bounds.endCol - bounds.startCol + 1;
    const totalRows = grid.height;
    const totalCols = grid.width;

    // Logique de détection automatique
    const isFullRowSelection = (bounds.startCol === 0 && bounds.endCol === totalCols - 1);
    const isFullColumnSelection = (bounds.startRow === 0 && bounds.endRow === totalRows - 1);
    
    try {
      if (isFullRowSelection && !isFullColumnSelection) {
        // Supprimer les lignes complètes
        if (totalRows > rowSpan) { // Garder au moins une ligne
          deleteRows(bounds);
          //const automationStore = useAutomationStore.getState();
          //automationStore.addCommand('DELETE ROWS', `${bounds.startRow},${bounds.endRow}`);
          applyActionToSelection('DELETE ROWS');
          console.log('[NewModificationsToolbar] Auto-deleted rows:', bounds.startRow, 'to', bounds.endRow);
        } else {
          console.warn('[NewModificationsToolbar] Cannot delete all rows - at least one must remain');
        }
      } else if (isFullColumnSelection && !isFullRowSelection) {
        // Supprimer les colonnes complètes
        if (totalCols > colSpan) { // Garder au moins une colonne
          deleteColumns(bounds);
          //const automationStore = useAutomationStore.getState();
          //automationStore.addCommand('DELETE COLUMNS', `${bounds.startCol},${bounds.endCol}`);
          applyActionToSelection('DELETE COLUMNS');
          console.log('[NewModificationsToolbar] Auto-deleted columns:', bounds.startCol, 'to', bounds.endCol);
        } else {
          console.warn('[NewModificationsToolbar] Cannot delete all columns - at least one must remain');
        }
      } else if (isFullRowSelection && isFullColumnSelection) {
        // Sélection de toute la grille - supprimer les lignes par défaut
        if (totalRows > 1) {
          deleteRows(bounds);
          //const automationStore = useAutomationStore.getState();
          //automationStore.addCommand('DELETE ROWS', `${bounds.startRow},${bounds.endRow}`);
          applyActionToSelection('DELETE ROWS');
          console.log('[NewModificationsToolbar] Auto-deleted all selected rows (full grid selected)');
        }
      } else {
        // Sélection partielle - déterminer basé sur la forme
        if (rowSpan >= colSpan && totalRows > rowSpan) {
          // Plus de lignes que de colonnes sélectionnées et on peut supprimer les lignes
          deleteRows(bounds);
          //const automationStore = useAutomationStore.getState();
          //automationStore.addCommand('DELETE ROWS', `${bounds.startRow},${bounds.endRow}`);
          applyActionToSelection('DELETE ROWS');
          console.log('[NewModificationsToolbar] Auto-deleted rows (partial selection):', bounds.startRow, 'to', bounds.endRow);
        } else if (colSpan > rowSpan && totalCols > colSpan) {
          // Plus de colonnes que de lignes sélectionnées et on peut supprimer les colonnes
          deleteColumns(bounds);
          //const automationStore = useAutomationStore.getState();
          //automationStore.addCommand('DELETE COLUMNS', `${bounds.startCol},${bounds.endCol}`);
          applyActionToSelection('DELETE COLUMNS');
          console.log('[NewModificationsToolbar] Auto-deleted columns (partial selection):', bounds.startCol, 'to', bounds.endCol);
        } else {
          console.warn('[NewModificationsToolbar] Cannot auto-determine deletion type or would delete all rows/columns');
        }
      }
    } catch (error) {
      console.error('[NewModificationsToolbar] Failed to auto-delete:', error);
    }
  };

  // Fonction générique pour appliquer une action à la dernière sélection
  const applyActionToSelection = (action: string, params?: string) => {
    const automationStore = useAutomationStore.getState();
    const currentCommands = automationStore.getCurrentCommands();
    // Chercher la dernière commande SELECT (syntaxe unifiée)
    const lastSelectIndex = [...currentCommands].reverse().findIndex(cmd => cmd.trim().startsWith('SELECT '));
    if (!automationStore.isRecording)
      return;
    else
      if (lastSelectIndex === -1) {
        console.warn('[NewModificationsToolbar] Aucune commande SELECT trouvée pour appliquer l\'action.');
        return;
      }

    // Index réel dans le tableau (car reverse)
    const selectIndex = currentCommands.length - 1 - lastSelectIndex;
    const selectCommand = currentCommands[selectIndex];
    // Remplacer SELECT par l’action et ajouter le paramètre si besoin
    let newCommand = '';
    if (params) {
      newCommand = selectCommand.replace(/^SELECT\b/, `${action} ${params}`);
    } else {
      newCommand = selectCommand.replace(/^SELECT\b/, action);
    }
    // Remplacer la commande SELECT par la nouvelle commande d’action
    const updatedCommands = [
      ...currentCommands.slice(0, selectIndex),
      newCommand,
      ...currentCommands.slice(selectIndex + 1)
    ];
    automationStore.setCommands(updatedCommands);
  };

  // Gestionnaire pour extraire la sélection
  const handleExtractSelection = () => {
    const grid = getCurrentGrid();
    if (!grid || !hasSelection) return;

    const bounds = getSelectionBounds();
    if (!bounds) return;

    // Extraire visuellement la zone sélectionnée
    const { startRow, endRow, startCol, endCol } = bounds;
    const extractedHeight = endRow - startRow + 1;
    const extractedWidth = endCol - startCol + 1;

    // Créer une nouvelle grille avec seulement la zone extraite
    const newGrid = new Grid(extractedHeight, extractedWidth);

    for (let row = 0; row < extractedHeight; row++) {
      for (let col = 0; col < extractedWidth; col++) {
        const originalRow = startRow + row;
        const originalCol = startCol + col;
        const cellValue = grid.getCell(originalRow, originalCol);
        newGrid.setCell(row, col, cellValue??0);
      }
    }

    // Remplacer la grille actuelle par la zone extraite
    replaceCurrentGrid(newGrid);

    // Appliquer la logique générique
    applyActionToSelection('EXTRACT');
  }



  return (
    <div className={toolbarStyles.toolbarContainer}>
      <span className={toolbarStyles.toolbarLabel}>Modifications :</span>

      <button
        className={toolbarStyles.toolbarButton}
        onClick={handleAdd}
        disabled={!hasSelection}
        title="Ajouter des lignes ou colonnes avec dialogue de configuration"
      >
        Ajouter
      </button>

      <button
        className={toolbarStyles.toolbarButton}
        onClick={handleAutoDelete}
        disabled={!hasSelection}
        title="Supprimer automatiquement les lignes ou colonnes sélectionnées"
      >
        Supprimer
      </button>

      <button
        className={toolbarStyles.toolbarButton}
        onClick={handleExtractSelection}
        disabled={!hasSelection}
        title="Extraire la sélection de la grille"
      >
        Extraire
      </button>



      {/* Dialogue d'ajout */}
      {showAddDialog && (
        <AddDialog
          onClose={() => setShowAddDialog(false)}
          onValidate={handleValidateAdd}
          initialType={addDialogInitialType}
        />
      )}
    </div>
  );
};