import { parseCoordinates } from './commandGenerationUtils';

/**
 * Formate une sélection spéciale selon les règles de la documentation
 * Supporte les nouveaux (INVERT, COLOR)
 */
export function formatSpecialSelection(
    selectionType: string,
    params: string,
    coordinates: string[]
): string {
    if (selectionType === 'COLOR') {
        // Nouveau format: (COLOR paramètres ([coord1] [coord2]))
        if (selectionType === 'COLOR') {
            let command = `SELECT (COLOR ${params} (`;
            if (coordinates.length > 0) {
                const coordStr = coordinates.map(coord => `[${coord}]`).join(' ');
                command += coordStr;
            }
            command += '))';
            return command;
        }
    } else if (selectionType === 'INVERT') {
        // Nouveau format: (INVERT ([coord1] [coord2]))
        if (selectionType === 'INVERT') {
            let command = 'SELECT (INVERT (';
            if (coordinates.length > 0) {
                const coordStr = coordinates.map(coord => `[${coord}]`).join(' ');
                command += coordStr;
            }
            command += '))';
            return command;
        }
    }
    
    return selectionType;
}

/**
 * Parse une sélection spéciale
 * Supporte les nouveaux (INVERT, COLOR)
 */
export function parseSpecialSelection(command: string): {
    type: string;
    parameters?: string;
    coordinates: string[];
} | null {
    // Pattern pour COLOR: (COLOR paramètres ([coord1] [coord2]))
    const colorPattern = /^\(COLOR\s+([^(]+)\s*\(([^)]+)\)\)$/;
    const colorMatch = command.match(colorPattern);
    
    if (colorMatch) {
        const params = colorMatch[1].trim();
        const coordsContent = colorMatch[2];
        const blockRegex = /\[([^\]]+)\]/g;
        const blocks = [];
        let match;
        while ((match = blockRegex.exec(coordsContent)) !== null) {
            blocks.push(match[1]);
        }
        
        const coordinates: string[] = [];
        for (const block of blocks) {
            coordinates.push(...parseCoordinates(block));
        }
        
        return {
            type: 'COLOR',
            parameters: params,
            coordinates
        };
    }
    
    // Pattern pour INVERT: (INVERT ([coord1] [coord2]))
    const invertPattern = /^\(INVERT\s*\(([^)]+)\)\)$/;
    const invertMatch = command.match(invertPattern);
    
    if (invertMatch) {
        const coordsContent = invertMatch[1];
        const blockRegex = /\[([^\]]+)\]/g;
        const blocks = [];
        let match;
        while ((match = blockRegex.exec(coordsContent)) !== null) {
            blocks.push(match[1]);
        }
        
        const coordinates: string[] = [];
        for (const block of blocks) {
            coordinates.push(...parseCoordinates(block));
        }
        
        return {
            type: 'INVERT',
            coordinates
        };
    }
    return null;
}

/**
 * Vérifie si une action est compatible avec INVERT
 */
export function isCompatibleWithSelectInvert(action: string): boolean {
    const compatibleActions = ['CLEAR', 'FILL', 'REPLACE', 'DELETE'];
    return compatibleActions.includes(action.toUpperCase());
}

/**
 * Vérifie si une action est compatible avec COLOR
 */
export function isCompatibleWithSelectColor(action: string): boolean {
    const compatibleActions = ['CLEAR', 'FILL', 'SURROUND', 'REPLACE', 'COPY', 'CUT'];
    return compatibleActions.includes(action.toUpperCase());
}

/**
 * Vérifie si une action est incompatible avec les sélections spéciales
 */
export function isIncompatibleWithSpecialSelections(action: string): boolean {
    const incompatibleActions = [
        'EXTRACT', 'INSERT', 'PASTE', 
        'ROTATE', 'FLIP', 'SURROUND', 'RESIZE'
    ];
    return incompatibleActions.some(incompatible => 
        action.toUpperCase().includes(incompatible)
    );
}

/**
 * Extrait une sélection spéciale d'une commande intégrée
 */
export function extractSpecialSelection(command: string): {
    baseCommand: string;
    specialSelection: {
        type: string;
        parameters?: string;
        coordinates: string[];
    } | null;
} {
    // Pattern pour nouveaux formats avec modificateurs de coordonnées
    // ACTION (INVERT ([coords])) ou ACTION (COLOR params ([coords]))
    const newModifierPattern = /^([A-Z_]+)\s+([^(]*?)\s*\(((?:INVERT|COLOR)\s*[^)]*)\)$/;
    const newMatch = command.match(newModifierPattern)? command.match(newModifierPattern) : null;
    if (!newMatch) {
        return {
            baseCommand: command,
            specialSelection: null
        };
    }
    const action = newMatch[1];
    const params = newMatch[2].trim();
    const modifierContent = newMatch[3];
    
    // Construire la commande de base
    let baseCommand = action;
    if (params) {
        baseCommand += ` ${params}`;
    }
    
    // Parser le modificateur
    let specialSelection = null;
    
    // Pattern pour INVERT: INVERT ([coords])
    const invertMatch = modifierContent.match(/^INVERT\s*\(([^)]+)\)$/);
    if (invertMatch) {
        const coordsContent = invertMatch[1];
        const blockRegex = /\[([^\]]+)\]/g;
        const blocks = [];
        let blockMatch;
        while ((blockMatch = blockRegex.exec(coordsContent)) !== null) {
            blocks.push(blockMatch[1]);
        }
        
        const coordinates: string[] = [];
        for (const block of blocks) {
            coordinates.push(...parseCoordinates(block));
        }
        
        specialSelection = {
            type: 'INVERT',
            coordinates
        };
    }
    
    // Pattern pour COLOR: COLOR params ([coords])
    const colorMatch = modifierContent.match(/^COLOR\s+([^(]+)\s*\(([^)]+)\)$/);
    if (colorMatch) {
        const colorParams = colorMatch[1].trim();
        const coordsContent = colorMatch[2];
        const blockRegex = /\[([^\]]+)\]/g;
        const blocks = [];
        let blockMatch;
        while ((blockMatch = blockRegex.exec(coordsContent)) !== null) {
            blocks.push(blockMatch[1]);
        }
        
        const coordinates: string[] = [];
        for (const block of blocks) {
            coordinates.push(...parseCoordinates(block));
        }
        
        specialSelection = {
            type: 'COLOR',
            parameters: colorParams,
            coordinates
        };
    }
    
    return {
        baseCommand,
        specialSelection
    };
}


/**
 * Détermine les actions disponibles selon le type de sélection
 */
export function getAvailableActionsForSelection(selectionType: string): string[] {
    if (selectionType === 'INVERT') {
        return ['CLEAR', 'FILL', 'REPLACE', 'DELETE'];
    }
    
    if (selectionType === 'COLOR') {
        return ['CLEAR', 'FILL', 'SURROUND', 'REPLACE', 'COPY', 'CUT'];
    }
    
    // Sélection normale - toutes les actions disponibles
    return [
        'CLEAR', 'FILL', 'EDIT', 'SURROUND', 'REPLACE',
        'COPY', 'CUT', 'PASTE', 'FLIP', 'ROTATE',
        'INSERT', 'DELETE', 'EXTRACT', 'MULTIPLY', 'DIVIDE'
    ];
}