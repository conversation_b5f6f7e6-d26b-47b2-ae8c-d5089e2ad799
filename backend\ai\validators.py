from typing import List, Dict, Any, Optional
import os
import sys
import django

# Configuration Django pour accéder aux modèles
if not hasattr(django.conf.settings, 'configured') or not django.conf.settings.configured:
    # Ajouter le répertoire backend au PATH Python
    backend_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), '..')
    if backend_path not in sys.path:
        sys.path.insert(0, backend_path)
      # Configuration minimale de Django
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'myproject.settings')
    django.setup()

from myapp.models import Command
from command_system.command_executor import CommandExecutor

class CommandValidator:
    """Validateur pour les commandes d'automatisation"""

    def __init__(self):
        """Initialise le validateur"""
        self.executor = CommandExecutor()
        self._commands_cache = None
        self._cache_timestamp = None

    def _get_commands_from_db(self):
        """Récupère les commandes actives depuis la base de données avec cache"""
        import time
        
        current_time = time.time()
        # Cache pendant 5 minutes
        if (self._commands_cache is None or
            self._cache_timestamp is None or
            current_time - self._cache_timestamp > 300):
            
            try:
                # Récupérer toutes les commandes actives avec leurs relations
                commands = Command.objects.filter(is_active=True).select_related('category')
                self._commands_cache = {cmd.name.upper(): cmd for cmd in commands}
                self._cache_timestamp = current_time
            except Exception as e:
                # Fallback vers les commandes codées en dur en cas d'erreur DB
                print(f"Erreur lors de la récupération des commandes: {e}")
                self._commands_cache = self._get_fallback_commands()
                self._cache_timestamp = current_time
                
        return self._commands_cache
    
    def _get_fallback_commands(self):
        """Commandes de secours si la DB n'est pas accessible"""
        # Mock des commandes essentielles pour compatibilité
        class MockCommand:
            def __init__(self, name, min_params, max_params, param_types=None):
                self.name = name
                self.min_params = min_params
                self.max_params = max_params
                self.param_types = param_types or []
                
            def validate_syntax(self, command_parts):
                param_count = len(command_parts) - 1
                if param_count < self.min_params:
                    return False, f"Commande {self.name} nécessite au moins {self.min_params} paramètres"
                if self.max_params is not None and param_count > self.max_params:
                    return False, f"Commande {self.name} accepte au maximum {self.max_params} paramètres"
                return True, "Validation réussie"
        
        return {
            # Commandes de base standardisées
            'INIT': MockCommand('INIT', 2, 2, ['int', 'int']),
            'END': MockCommand('END', 0, 0),  # Remplace PROPOSE et VALIDATE
            'TRANSFERT': MockCommand('TRANSFERT', 1, None, ['group']),
            
            # Commandes d'édition
            'EDIT': MockCommand('EDIT', 3, 3, ['int', 'int', 'int']),
            'FILL': MockCommand('FILL', 5, 5, ['int', 'int', 'int', 'int', 'int']),
            'FLOODFILL': MockCommand('FLOODFILL', 3, 3, ['int', 'int', 'int']),
            'REPLACE': MockCommand('REPLACE', 4, 4, ['int', 'int', 'coords', 'coords']),
            'CLEAR': MockCommand('CLEAR', 4, 4, ['int', 'int', 'int', 'int']),
            
            # Commandes de sélection
            'SELECT': MockCommand('SELECT', 1, 4, ['mixed']),
            
            # Commandes de transformation standardisées
            'COPY': MockCommand('COPY', 6, 7, ['int', 'int', 'int', 'int', 'int', 'int']),
            'CUT': MockCommand('CUT', 2, 2, ['coords', 'coords']),
            'PASTE': MockCommand('PASTE', 1, 1, ['coords']),
            'MOVE': MockCommand('MOVE', 6, 7, ['int', 'int', 'int', 'int', 'int', 'int']),
            'FLIP': MockCommand('FLIP', 3, 3, ['axis', 'coords', 'coords']),
            'ROTATE': MockCommand('ROTATE', 1, 3, ['direction', 'coords', 'coords']),  # Standardisé
            'INSERT': MockCommand('INSERT', 2, 2, ['type', 'int']),  # Standardisé
            'DELETE': MockCommand('DELETE', 2, 2, ['type', 'int']),  # Standardisé
            'RESIZE': MockCommand('RESIZE', 1, 1, ['dimensions']),

            # Commandes de multiplication/division
            'MULTIPLY': MockCommand('MULTIPLY', 2, 2, ['int', 'coords']),
            'DIVIDE': MockCommand('DIVIDE', 2, 2, ['int', 'coords']),

            # Commandes de regroupement
            'EDITS': MockCommand('EDITS', 1, None, ['group']),
            'FILLS': MockCommand('FILLS', 1, None, ['group']),
            'FLOODFILLS': MockCommand('FLOODFILLS', 1, None, ['group']),
            'REPLACES': MockCommand('REPLACES', 1, None, ['group']),
            'SELECTS': MockCommand('SELECTS', 1, None, ['group']),
            'FLIPS': MockCommand('FLIPS', 1, None, ['group']),
            'ROTATES': MockCommand('ROTATES', 1, None, ['group']),
            'INSERTS': MockCommand('INSERTS', 1, None, ['group']),
            'DELETES': MockCommand('DELETES', 1, None, ['group']),
        }

    def validate_commands(self, commands: List[str], task=None) -> Dict[str, Any]:
        """Valide une liste de commandes d'automatisation"""
        valid_commands = []
        errors = []

        # Vérifier la syntaxe de chaque commande
        for i, command in enumerate(commands):
            try:
                # Valider la syntaxe de la commande
                is_valid, error_msg = self._validate_syntax(command)
                if is_valid:
                    valid_commands.append(command)
                else:
                    errors.append({
                        'line': i + 1,
                        'command': command,
                        'error': error_msg
                    })
            except Exception as e:
                errors.append({
                    'line': i + 1,
                    'command': command,
                    'error': str(e)
                })

        # Si des erreurs de syntaxe ont été trouvées, retourner immédiatement
        if errors:
            return {
                'valid': False,
                'valid_commands': valid_commands,
                'errors': errors,
                'execution_result': None
            }

        # Exécuter les commandes
        execution_result = self.executor.execute_commands(valid_commands)

        # Si l'exécution a échoué, ajouter l'erreur
        if not execution_result['success']:
            errors.append({
                'line': -1,  # Ligne inconnue
                'command': '',
                'error': execution_result['error']
            })

        # Si une tâche est fournie, valider la solution
        validation_result = None
        if task and execution_result['success']:
            # Récupérer la sortie attendue du premier exemple de test
            if task.get_data() and 'test' in task.get_data() and task.get_data()['test']:
                expected_output = task.get_data()['test'][0]['output']
                validation_result = self.executor.validate_solution(valid_commands, expected_output)

                # Si la validation a échoué, ajouter l'erreur
                if not validation_result['success']:
                    errors.append({
                        'line': -1,  # Ligne inconnue
                        'command': '',
                        'error': validation_result['error']
                    })

        return {
            'valid': len(errors) == 0,
            'valid_commands': valid_commands,
            'errors': errors,
            'execution_result': execution_result,
            'validation_result': validation_result
        }

    def _validate_syntax(self, command: str) -> tuple[bool, str]:
        """Valide la syntaxe d'une commande en utilisant les modèles Django"""
        parts = command.strip().split()

        if not parts:
            return False, "Commande vide"

        cmd_type = parts[0].upper()
        
        # Récupérer les définitions de commandes depuis la DB
        commands_db = self._get_commands_from_db()
        
        # Vérifier si la commande existe
        if cmd_type not in commands_db:
            available_commands = list(commands_db.keys())
            return False, f"Commande '{cmd_type}' inconnue. Commandes disponibles: {', '.join(available_commands)}"
        
        # Utiliser la méthode validate_syntax du modèle Command
        command_obj = commands_db[cmd_type]
        return command_obj.validate_syntax(parts)