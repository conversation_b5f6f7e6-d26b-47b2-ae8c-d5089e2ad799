#!/usr/bin/env python
"""
Test des commandes MULTIPLY et DIVIDE avec presse-papier et masques
"""

import os
import sys
import django
import numpy as np

# Configuration Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from command_system.command_executor import CommandExecutor
from command_system.unified_command import UnifiedCommand


def test_multiply_with_clipboard():
    """Test de MULTIPLY avec presse-papier et masque"""
    print("🧪 Test MULTIPLY avec presse-papier")
    
    # Créer un exécuteur avec une grille de test 9x9
    executor = CommandExecutor()
    executor.grid = np.zeros((9, 9), dtype=int)
    executor.height, executor.width = executor.grid.shape
    
    # C<PERSON><PERSON> le motif initial 3x3 comme dans l'exemple
    # Pattern: 7 0 7
    #          7 0 7  
    #          7 7 0
    executor.grid[0, 0] = 7  # Orange
    executor.grid[0, 2] = 7  # Orange
    executor.grid[1, 0] = 7  # Orange
    executor.grid[1, 2] = 7  # Orange
    executor.grid[2, 0] = 7  # Orange
    executor.grid[2, 1] = 7  # Orange
    
    print("Grille initiale 3x3:")
    print(executor.grid[:3, :3])
    
    # Simuler COPY (COLOR 7 [0,0 2,2])
    # Copier seulement les cellules de couleur 7
    clipboard = np.zeros((3, 3), dtype=int)
    clipboard_mask = np.zeros((3, 3), dtype=bool)
    
    for i in range(3):
        for j in range(3):
            if executor.grid[i, j] == 7:
                clipboard[i, j] = 7
                clipboard_mask[i, j] = True
            else:
                clipboard[i, j] = 0
                clipboard_mask[i, j] = False
    
    executor.clipboard = clipboard
    executor.clipboard_mask = clipboard_mask
    
    print("\nContenu du presse-papier:")
    print(clipboard)
    print("\nMasque du presse-papier:")
    print(clipboard_mask.astype(int))
    
    # Test MULTIPLY 3 [0,0 8,8]
    print("\n--- Test: MULTIPLY 3 [0,0 8,8] ---")
    cmd = UnifiedCommand.parse("MULTIPLY 3 [0,0 8,8]")
    if cmd:
        result = executor._cmd_multiply(cmd)
        print(f"Résultat: {result}")
        if result:
            print("Grille après multiplication:")
            print(executor.grid)
            
            # Vérifier que le motif a été correctement multiplié
            expected_positions = [
                # Motif original multiplié par 3
                (0,0), (0,2), (1,0), (1,2), (2,0), (2,1),  # Bloc 1
                (0,3), (0,5), (1,3), (1,5), (2,3), (2,4),  # Bloc 2
                (0,6), (0,8), (1,6), (1,8), (2,6), (2,7),  # Bloc 3
                (3,0), (3,2), (4,0), (4,2), (5,0), (5,1),  # Bloc 4
                (3,3), (3,5), (4,3), (4,5), (5,3), (5,4),  # Bloc 5
                (3,6), (3,8), (4,6), (4,8), (5,6), (5,7),  # Bloc 6
                (6,0), (6,2), (7,0), (7,2), (8,0), (8,1),  # Bloc 7
                (6,3), (6,5), (7,3), (7,5), (8,3), (8,4),  # Bloc 8
                (6,6), (6,8), (7,6), (7,8), (8,6), (8,7),  # Bloc 9
            ]
            
            correct_count = 0
            for row, col in expected_positions:
                if executor.grid[row, col] == 7:
                    correct_count += 1
                else:
                    print(f"❌ Position ({row},{col}) devrait être 7, mais est {executor.grid[row, col]}")
            
            print(f"\n✅ {correct_count}/{len(expected_positions)} positions correctes")
            return correct_count == len(expected_positions)
        else:
            print(f"Erreur: {executor.error}")
    
    return False


def test_divide_with_clipboard():
    """Test de DIVIDE avec presse-papier et masque"""
    print("\n🧪 Test DIVIDE avec presse-papier")
    
    # Créer un exécuteur avec une grille de test 6x6
    executor = CommandExecutor()
    executor.grid = np.zeros((6, 6), dtype=int)
    executor.height, executor.width = executor.grid.shape
    
    # Créer un motif 6x6 qui peut être divisé par 2
    pattern = np.array([
        [7, 7, 0, 0, 7, 7],
        [7, 7, 0, 0, 7, 7],
        [0, 0, 7, 7, 0, 0],
        [0, 0, 7, 7, 0, 0],
        [7, 7, 0, 0, 7, 7],
        [7, 7, 0, 0, 7, 7]
    ])
    
    executor.grid = pattern.copy()
    
    print("Grille initiale 6x6:")
    print(executor.grid)
    
    # Simuler COPY (COLOR 7 [0,0 5,5])
    clipboard = np.zeros((6, 6), dtype=int)
    clipboard_mask = np.zeros((6, 6), dtype=bool)
    
    for i in range(6):
        for j in range(6):
            if executor.grid[i, j] == 7:
                clipboard[i, j] = 7
                clipboard_mask[i, j] = True
            else:
                clipboard[i, j] = 0
                clipboard_mask[i, j] = False
    
    executor.clipboard = clipboard
    executor.clipboard_mask = clipboard_mask
    
    print("\nContenu du presse-papier:")
    print(clipboard)
    
    # Test DIVIDE 2 [0,0 5,5]
    print("\n--- Test: DIVIDE 2 [0,0 5,5] ---")
    cmd = UnifiedCommand.parse("DIVIDE 2 [0,0 5,5]")
    if cmd:
        result = executor._cmd_divide(cmd)
        print(f"Résultat: {result}")
        if result:
            print("Grille après division:")
            print(executor.grid)
            
            # Vérifier que le motif a été correctement divisé
            expected = np.array([
                [7, 0, 7],
                [0, 7, 0],
                [7, 0, 7]
            ])
            
            actual = executor.grid[:3, :3]
            matches = np.array_equal(actual, expected)
            print(f"\n✅ Division correcte: {matches}")
            if not matches:
                print("Attendu:")
                print(expected)
                print("Obtenu:")
                print(actual)
            
            return matches
        else:
            print(f"Erreur: {executor.error}")
    
    return False


def main():
    """Fonction principale de test"""
    print("=== Test des commandes MULTIPLY et DIVIDE avec presse-papier ===")
    
    tests = [
        ("MULTIPLY avec masque", test_multiply_with_clipboard),
        ("DIVIDE avec masque", test_divide_with_clipboard)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
            print(f"\n✅ Test {test_name}: {'RÉUSSI' if result else 'ÉCHOUÉ'}")
        except Exception as e:
            results.append((test_name, False))
            print(f"\n❌ Test {test_name}: ERREUR - {e}")
            import traceback
            traceback.print_exc()
    
    print("\n=== Résumé des tests ===")
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ RÉUSSI" if result else "❌ ÉCHOUÉ"
        print(f"{test_name}: {status}")
    
    print(f"\nRésultat global: {passed}/{total} tests réussis")
    
    if passed == total:
        print("🎉 Tous les tests sont passés !")
        return True
    else:
        print("⚠️ Certains tests ont échoué")
        return False


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
