// src/components/resolution/Toolbar/TransformToolbar.tsx
import React from 'react';
import { useSelection, useClipboard } from '../hooks'; // useResolutionStore retiré
import { useAutomationStore } from '../hooks/useAutomationStore';
import { useGridStateStore } from '../hooks/useGridStateStore';
import {
  rotateClipboardRight,
  rotateClipboardLeft,
  flipClipboardHorizontal,
  flipClipboardVertical,
  multiplyClipboard,
  divideClipboard
} from '../utils/clipboardTransformUtils';
import { transformationCoordinator } from '../utils/transformationCoordinator';
import styles from './Toolbar.module.css';

/**
 * Barre d'outils pour les transformations (rotation, symétrie, etc.)
 * Gère automatiquement les deux modes :
 * - Mode sélection (blanche) : transformation directe de la grille
 * - Mode motif (rouge) : transformation du motif uniquement
 */
export const TransformToolbar: React.FC = () => {
  // Récupérer l'état et les fonctions de sélection
  const { selectedCells, getSelectionBounds } = useSelection();

  // Récupérer l'état du presse-papier
  const { content, moveUp, moveDown, moveLeft, moveRight } = useClipboard();

  // Déterminer le mode de transformation
  const isMotifMode = content !== null;
  const isSelectionMode = selectedCells.size > 0 && !isMotifMode;

  // Activer les boutons selon le contexte
  const canTransform = isMotifMode || isSelectionMode;

  // NOTE: Les anciennes fonctions utilitaires (extractSubgrid, applySubgrid, etc.)
  // ont été supprimées car elles sont maintenant gérées par le TransformationCoordinator
  // qui garantit la synchronisation correcte entre sélection et transformation de grille.

  // Gestionnaire unifié pour la rotation à droite (VERSION SYNCHRONISÉE)
  const handleRotateRight = async () => {
    console.log('[TransformToolbar] handleRotateRight called - isMotifMode:', isMotifMode, 'canTransform:', canTransform);
    console.log('[TransformToolbar] selectedCells.size:', selectedCells.size);
    
    if (isMotifMode) {
      // Mode motif : transformer le motif ET synchroniser la sélection
      const clipboardStore = useClipboard.getState();
      const selectionStore = useSelection.getState();
      const automationStore = useAutomationStore.getState();
      
      rotateClipboardRight(
        useClipboard.setState, 
        useClipboard.getState, 
        selectionStore, 
        automationStore
      );
    } else if (isSelectionMode) {
      // Mode sélection : utiliser le coordinateur synchronisé
      try {
        const bounds = getSelectionBounds();
        if (!bounds) {
          console.warn('[Transform] No selection bounds available for rotation');
          return;
        }

        console.log('[Transform] Starting synchronized rotation right:', bounds);
        
        const result = await transformationCoordinator.executeTransformation('rotateRight', bounds);
        
        if (!result.success) {
          console.error('[Transform] Synchronized rotation failed:', result.error);
          // Optionnel : afficher une notification d'erreur à l'utilisateur
        } else {
          console.log('[Transform] Synchronized rotation completed successfully');
        }
      } catch (error) {
        console.error('[Transform] Rotation error:', error);
      }
    }
  };

  // Gestionnaire unifié pour la rotation à gauche (VERSION SYNCHRONISÉE)
  const handleRotateLeft = async () => {
    if (isMotifMode) {
      // Mode motif : transformer le motif ET synchroniser la sélection
      const selectionStore = useSelection.getState();
      const automationStore = useAutomationStore.getState();
      
      rotateClipboardLeft(
        useClipboard.setState, 
        useClipboard.getState, 
        selectionStore, 
        automationStore
      );
    } else if (isSelectionMode) {
      // Mode sélection : utiliser le coordinateur synchronisé
      try {
        const bounds = getSelectionBounds();
        if (!bounds) {
          console.warn('[Transform] No selection bounds available for rotation');
          return;
        }

        console.log('[Transform] Starting synchronized rotation left:', bounds);
        
        const result = await transformationCoordinator.executeTransformation('rotateLeft', bounds);
        
        if (!result.success) {
          console.error('[Transform] Synchronized rotation failed:', result.error);
          // Optionnel : afficher une notification d'erreur à l'utilisateur
        } else {
          console.log('[Transform] Synchronized rotation completed successfully');
        }
      } catch (error) {
        console.error('[Transform] Rotation error:', error);
      }
    }
  };

  // Gestionnaire unifié pour le retournement horizontal (VERSION SYNCHRONISÉE)
  const handleFlipHorizontal = async () => {
    if (isMotifMode) {
      // Mode motif : transformer le motif ET synchroniser la sélection
      const selectionStore = useSelection.getState();
      const automationStore = useAutomationStore.getState();
      
      flipClipboardHorizontal(
        useClipboard.setState, 
        useClipboard.getState, 
        selectionStore, 
        automationStore
      );
    } else if (isSelectionMode) {
      // Mode sélection : utiliser le coordinateur synchronisé
      try {
        const bounds = getSelectionBounds();
        if (!bounds) {
          console.warn('[Transform] No selection bounds available for horizontal flip');
          return;
        }

        console.log('[Transform] Starting synchronized horizontal flip:', bounds);
        
        const result = await transformationCoordinator.executeTransformation('flipHorizontal', bounds);
        
        if (!result.success) {
          console.error('[Transform] Synchronized horizontal flip failed:', result.error);
          // Optionnel : afficher une notification d'erreur à l'utilisateur
        } else {
          console.log('[Transform] Synchronized horizontal flip completed successfully');
        }
      } catch (error) {
        console.error('[Transform] Horizontal flip error:', error);
      }
    }
  };

  // Gestionnaire unifié pour le retournement vertical (VERSION SYNCHRONISÉE)
  const handleFlipVertical = async () => {
    if (isMotifMode) {
      // Mode motif : transformer le motif ET synchroniser la sélection
      const selectionStore = useSelection.getState();
      const automationStore = useAutomationStore.getState();
      
      flipClipboardVertical(
        useClipboard.setState, 
        useClipboard.getState, 
        selectionStore, 
        automationStore
      );
    } else if (isSelectionMode) {
      // Mode sélection : utiliser le coordinateur synchronisé
      try {
        const bounds = getSelectionBounds();
        if (!bounds) {
          console.warn('[Transform] No selection bounds available for vertical flip');
          return;
        }

        console.log('[Transform] Starting synchronized vertical flip:', bounds);
        
        const result = await transformationCoordinator.executeTransformation('flipVertical', bounds);
        
        if (!result.success) {
          console.error('[Transform] Synchronized vertical flip failed:', result.error);
          // Optionnel : afficher une notification d'erreur à l'utilisateur
        } else {
          console.log('[Transform] Synchronized vertical flip completed successfully');
        }
      } catch (error) {
        console.error('[Transform] Vertical flip error:', error);
      }
    }
  };

  // Gestionnaires pour les déplacements de motif
  const handleMoveUp = () => {
    console.log('[TransformToolbar] handleMoveUp called, isMotifMode:', isMotifMode);
    if (isMotifMode) {
      moveUp();
    }
  };

  const handleMoveDown = () => {
    console.log('[TransformToolbar] handleMoveDown called, isMotifMode:', isMotifMode);
    if (isMotifMode) {
      moveDown();
    }
  };

  const handleMoveLeft = () => {
    console.log('[TransformToolbar] handleMoveLeft called, isMotifMode:', isMotifMode);
    if (isMotifMode) {
      moveLeft();
    }
  };

  const handleMoveRight = () => {
    console.log('[TransformToolbar] handleMoveRight called, isMotifMode:', isMotifMode);
    if (isMotifMode) {
      moveRight();
    }
  };

  // Gestionnaire pour multiplier la sélection/motif
  const handleMultiply = async () => {
    console.log('[TransformToolbar] handleMultiply called, isMotifMode:', isMotifMode);

    // Demander le facteur de multiplication
    const factorStr = prompt('Facteur de multiplication (2-10):', '2');
    if (!factorStr) return;

    const factor = parseInt(factorStr);
    if (isNaN(factor) || factor < 2 || factor > 10) {
      alert('Le facteur doit être un nombre entre 2 et 10');
      return;
    }

    if (isMotifMode) {
      // Mode motif : transformer le motif ET synchroniser la sélection
      const selectionStore = useSelection.getState();
      const automationStore = useAutomationStore.getState();

      multiplyClipboard(
        factor,
        useClipboard.setState,
        useClipboard.getState,
        selectionStore,
        automationStore
      );
    } else if (isSelectionMode) {
      // Mode sélection : MULTIPLY nécessite un presse-papier, donc on doit d'abord faire un COPY
      alert('MULTIPLY nécessite un motif dans le presse-papier. Utilisez d\'abord COPY puis passez en mode motif (rouge).');
      return;
    }
  };

  // Gestionnaire pour diviser la sélection/motif
  const handleDivide = async () => {
    console.log('[TransformToolbar] handleDivide called, isMotifMode:', isMotifMode);

    // Demander le facteur de division
    const factorStr = prompt('Facteur de division (2-10):', '2');
    if (!factorStr) return;

    const factor = parseInt(factorStr);
    if (isNaN(factor) || factor < 2 || factor > 10) {
      alert('Le facteur doit être un nombre entre 2 et 10');
      return;
    }

    if (isMotifMode) {
      // Mode motif : transformer le motif ET synchroniser la sélection
      const selectionStore = useSelection.getState();
      const automationStore = useAutomationStore.getState();

      divideClipboard(
        factor,
        useClipboard.setState,
        useClipboard.getState,
        selectionStore,
        automationStore
      );
    } else if (isSelectionMode) {
      // Mode sélection : DIVIDE nécessite un presse-papier, donc on doit d'abord faire un COPY
      alert('DIVIDE nécessite un motif dans le presse-papier. Utilisez d\'abord COPY puis passez en mode motif (rouge).');
      return;
    }
  };

  // Gestionnaire pour le collage
  return (
    <div className={styles.toolbarContainer}>
      <span className={styles.toolbarLabel}>
        Transformations :
      </span>

      {/* Boutons de déplacement - uniquement en mode motif */}
      {isMotifMode && (
        <>
          <button
            className={styles.toolbarButton}
            onClick={handleMoveUp}
            disabled={!canTransform}
            title="Déplacer le motif vers le haut"
          >
            ↑
          </button>

          <button
            className={styles.toolbarButton}
            onClick={handleMoveDown}
            disabled={!canTransform}
            title="Déplacer le motif vers le bas"
          >
            ↓
          </button>

          <button
            className={styles.toolbarButton}
            onClick={handleMoveLeft}
            disabled={!canTransform}
            title="Déplacer le motif vers la gauche"
          >
            ←
          </button>

          <button
            className={styles.toolbarButton}
            onClick={handleMoveRight}
            disabled={!canTransform}
            title="Déplacer le motif vers la droite"
          >
            →
          </button>
        </>
      )}

      <button
        className={styles.toolbarButton}
        onClick={handleRotateRight}
        disabled={!canTransform}
        title="Rotation à droite (90° sens horaire)"
      >
        ↻
      </button>

      <button
        className={styles.toolbarButton}
        onClick={handleRotateLeft}
        disabled={!canTransform}
        title="Rotation à gauche (90° sens antihoraire)"
      >
        ↺
      </button>

      <button
        className={styles.toolbarButton}
        onClick={handleFlipHorizontal}
        disabled={!canTransform}
        title="Retournement horizontal"
      >
        ↔
      </button>

      <button
        className={styles.toolbarButton}
        onClick={handleFlipVertical}
        disabled={!canTransform}
        title="Retournement vertical"
      >
        ↕
      </button>

      <button
        className={styles.toolbarButton}
        onClick={handleMultiply}
        disabled={!canTransform}
        title="Multiplier la sélection/motif par un facteur"
      >
        ×
      </button>

      <button
        className={styles.toolbarButton}
        onClick={handleDivide}
        disabled={!canTransform}
        title="Diviser la sélection/motif par un facteur"
      >
        ÷
      </button>
    </div>
  );
};
