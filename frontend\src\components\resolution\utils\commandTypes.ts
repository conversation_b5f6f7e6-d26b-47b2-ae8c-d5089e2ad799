/**
 * Types et interfaces pour le système de commandes unifiées
 */

export interface UnifiedCommand {
    action: string;
    parameters?: string;
    coordinates: string[];
    additionalCoordinates: string[];
    isMotif: boolean;
    isSpecialSelection: boolean;
    executionType?: string;
    subcommands?: UnifiedCommand[];
    specialSelection?: {
        type: string;
        parameters?: string;
    };
    raw?: string;
}

export interface OptimizationRule {
    name: string;
    apply: (commands: string[]) => string[];
    priority: number;
}

export interface GroupingRule {
    type: string;
    canGroup: (commands: UnifiedCommand[]) => boolean;
    format: (commands: UnifiedCommand[]) => string;
}

export interface SpecialSelection {
    type: 'COLOR' | 'INVERT';
    parameters?: string;
    coordinates: string[];
}

export interface ParsedGroupedCommand {
    groupType: string;
    commands: string[];
}

export interface CommandParseResult {
    baseCommand: string;
    specialSelection: SpecialSelection | null;
}

export interface OptimizationResult {
    optimizedCommands: string[];
    appliedRules: string[];
    removedCommands: string[];
}

export interface GroupingResult {
    groupedCommands: string[];
    appliedGroupings: string[];
}

/**
 * Types d'actions supportées
 */
export type ActionType =
    | 'CLEAR' | 'FILL' | 'FLOODFILL' | 'EDIT' | 'SURROUND' | 'REPLACE'
    | 'COPY' | 'CUT' | 'PASTE'
    | 'FLIP' | 'ROTATE'
    | 'INSERT' | 'DELETE' | 'EXTRACT'
    | 'MULTIPLY' | 'DIVIDE'
    | 'RESIZE' | 'INIT' | 'END'
    | 'TRANSFERT' | 'MOTIF';

/**
 * Types de motifs
 */
export type MotifType = 
    | 'COPY' | 'PASTE' | 'CUT' | 'FLIP' | 'ROTATE';

/**
 * Types de sélections spéciales
 */
export type SpecialSelectionType = 'COLOR' | 'INVERT';

/**
 * Types de regroupement
 */
export type GroupType = 'EDITS' | 'FILLS' |  'FLOODFILLS' | 'REPLACES' | 'CLEARS' | 'SURROUNDS' | 'DELETES' | 'TRANSFERT' | 'INSERTS' | 'DELETES | MOTIF';

/**
 * Configuration pour le parsing de commandes
 */
export interface ParseConfig {
    strictMode: boolean;
    allowLegacyFormat: boolean;
    validateCoordinates: boolean;
}

/**
 * Configuration pour l'optimisation
 */
export interface OptimizationConfig {
    enableGrouping: boolean;
    enableRedundantRemoval: boolean;
    enableSpecialSelectionIntegration: boolean;
    maxOptimizationPasses: number;
}

/**
 * Résultat de validation d'une commande
 */
export interface ValidationResult {
    isValid: boolean;
    errors: string[];
    warnings: string[];
}

/**
 * Métadonnées d'une commande
 */
export interface CommandMetadata {
    originalCommand: string;
    parsedAt: Date;
    optimized: boolean;
    grouped: boolean;
    hasSpecialSelection: boolean;
}

/**
 * Contexte d'exécution d'une commande
 */
export interface ExecutionContext {
    gridSize: { width: number; height: number };
    currentSelection: string[];
    clipboardContent?: any;
    history: string[];
}

/**
 * Erreur de parsing de commande
 */
export class CommandParseError extends Error {
    constructor(
        message: string,
        public command: string,
        public position?: number
    ) {
        super(message);
        this.name = 'CommandParseError';
    }
}

/**
 * Erreur d'optimisation
 */
export class OptimizationError extends Error {
    constructor(
        message: string,
        public ruleName: string,
        public commands: string[]
    ) {
        super(message);
        this.name = 'OptimizationError';
    }
}

/**
 * Erreur de regroupement
 */
export class GroupingError extends Error {
    constructor(
        message: string,
        public groupType: string,
        public commands: string[]
    ) {
        super(message);
        this.name = 'GroupingError';
    }
}

/**
 * Constantes pour les actions
 */
export const MOTIF_ACTIONS: MotifType[] = [
    'COPY', 'PASTE', 'CUT', 'FLIP', 'ROTATE'
];

export const SIMPLE_ACTIONS: ActionType[] = [
    'CLEAR', 'FILL', 'FLOODFILL', 'EDIT', 'SURROUND', 'REPLACE'
];

export const STRUCTURAL_ACTIONS: ActionType[] = [
    'INSERT', 'DELETE', 'EXTRACT', 'MULTIPLY', 'DIVIDE'
];

export const SYSTEM_ACTIONS: ActionType[] = [
    'RESIZE', 'INIT', 'END', 'TRANSFERT', 'MOTIF'
];

/**
 * Actions compatibles avec INVERT
 */
export const INVERT_COMPATIBLE: ActionType[] = [
    'CLEAR', 'FILL', 'REPLACE', 'DELETE'
];

/**
 * Actions compatibles avec COLOR
 */
export const COLOR_COMPATIBLE: ActionType[] = [
    'CLEAR', 'FILL', 'SURROUND', 'REPLACE'
];

/**
 * Actions incompatibles avec les sélections spéciales
 */
export const SPECIAL_SELECTION_INCOMPATIBLE: ActionType[] = [
    'EXTRACT', 'INSERT', 'COPY', 'CUT', 'PASTE', 'ROTATE', 'FLIP', 'SURROUND', 'MULTIPLY', 'DIVIDE'
];

/**
 * Patterns regex pour le parsing
 */
export const COMMAND_PATTERNS = {
    STANDARD: /^([A-Z_]+)(\s+([^\[\]]+))?\s*(\[[^\]]+\])?\s*(\[[^\]]+\])?$/,
    SELECT_INDIVIDUAL: /^SELECT\s*(\[[^\]]+\])$/,
    MULTI_SELECT: /^SELECT\s*\(([^)]+)\)$/,
    TRANSFERT: /^(TRANSFERT)\s*\{([^}\s].+)\}$/,
    MOTIF: /^(MOTIF)\s*\{([^}\s].+)\}$/,
    MOTIF_COORDINATES: /^(COPY|PASTE|CUT)\s*\((\[[^\]]+\])\)$/,
    MULTI_COORDINATES: /^([A-Z_]+)\s+([^(]*?)\s*\((.*)\)$/,
    SCENARIO_COORDINATES: /^([A-Z_]+)\s*\((\[[^\]]+\])\)$/,
    SCENARIO_SIMPLE: /^([A-Z_]+)\s+([^()\[\]]+)$/,
    INTEGRATED_SPECIAL: /^([A-Z]+)\s+([^(]*?)\s*\((SELECT (?:COLOR|INVERT))\s+([^)]+)\)$/,
    DIRECT_SPECIAL: /^([A-Z]+)\s*\((SELECT (?:COLOR|INVERT))\s+([^)]+)\)$/,
    GROUPED: /^([A-Z_]+S)\s*\{([^}]+)\}$/,
    COORDINATE_BLOCK: /\[([^\]]+)\]/g
};