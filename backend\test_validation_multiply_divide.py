#!/usr/bin/env python
"""
Test de validation des commandes MULTIPLY et DIVIDE
"""

import os
import sys
import django

# Configuration Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from ai.command_decompressor import CommandDecompressor
from command_system.unified_command import UnifiedCommand


def test_validation_multiply_divide():
    """Test que les commandes MULTIPLY et DIVIDE passent la validation"""
    print("🧪 Test de validation MULTIPLY et DIVIDE")
    
    decompressor = CommandDecompressor()
    
    test_commands = [
        "MULTIPLY 3 [0,0 2,2]",
        "DIVIDE 2 [0,0 3,3]",
        "MULTIPLY 2 (COLOR 1,2 [1,1 4,4])",
        "DIVIDE 3 (INVERT [2,2 5,5])"
    ]
    
    results = []
    
    for command in test_commands:
        print(f"\n--- Test: {command} ---")
        
        # Test 1: Validation individuelle
        is_valid = decompressor._is_valid_individual_command(command)
        print(f"Validation individuelle: {is_valid}")
        
        # Test 2: Parsing UnifiedCommand
        try:
            unified_cmd = UnifiedCommand.parse(command)
            if unified_cmd:
                syntax_valid = unified_cmd.validate_syntax()
                print(f"Parsing UnifiedCommand: ✅ (syntaxe: {syntax_valid})")
                print(f"  - Action: {unified_cmd.action}")
                print(f"  - Paramètres: {unified_cmd.parameters}")
                print(f"  - Coordonnées: {unified_cmd.coordinates}")
            else:
                print(f"Parsing UnifiedCommand: ❌")
                syntax_valid = False
        except Exception as e:
            print(f"Parsing UnifiedCommand: ❌ - {e}")
            syntax_valid = False
        
        # Test 3: Décompression
        try:
            decompressed = decompressor.decompress_commands([command])
            print(f"Décompression: ✅ ({len(decompressed)} commandes)")
        except Exception as e:
            print(f"Décompression: ❌ - {e}")
            decompressed = []
        
        success = is_valid and syntax_valid and len(decompressed) > 0
        results.append((command, success))
        print(f"Résultat global: {'✅ RÉUSSI' if success else '❌ ÉCHOUÉ'}")
    
    return results


def main():
    """Fonction principale"""
    print("=== Test de validation des commandes MULTIPLY et DIVIDE ===")
    
    results = test_validation_multiply_divide()
    
    print("\n=== Résumé ===")
    passed = 0
    total = len(results)
    
    for command, success in results:
        status = "✅ RÉUSSI" if success else "❌ ÉCHOUÉ"
        print(f"{command}: {status}")
        if success:
            passed += 1
    
    print(f"\nRésultat global: {passed}/{total} tests réussis")
    
    if passed == total:
        print("🎉 Toutes les validations sont passées !")
        return True
    else:
        print("⚠️ Certaines validations ont échoué")
        return False


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
