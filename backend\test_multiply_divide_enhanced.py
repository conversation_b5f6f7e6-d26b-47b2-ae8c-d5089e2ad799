#!/usr/bin/env python
"""
Test des commandes MULTIPLY et DIVIDE améliorées
"""

import os
import sys
import django

# Configuration Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from command_system.command_executor import CommandExecutor
from command_system.unified_command import UnifiedCommand
import numpy as np


def test_multiply_with_replace_option():
    """Test de MULTIPLY avec l'option replace"""
    print("🧪 Test de MULTIPLY avec option replace")
    
    executor = CommandExecutor()
    executor.grid = np.array([
        [1, 2, 0, 0, 0, 0],
        [3, 4, 0, 0, 0, 0],
        [0, 0, 0, 0, 0, 0],
        [0, 0, 0, 0, 0, 0],
        [0, 0, 0, 0, 0, 0],
        [0, 0, 0, 0, 0, 0]
    ])
    executor.height, executor.width = executor.grid.shape
    
    print("Grille initiale:")
    print(executor.grid)
    
    # Test 1: MULTIPLY avec replace=true (par défaut)
    print("\n--- Test 1: MULTIPLY 2 true [0,0 1,1] ---")
    cmd1 = UnifiedCommand.parse("MULTIPLY 2 true [0,0 1,1]")
    if cmd1:
        result1 = executor._cmd_multiply(cmd1)
        print(f"Résultat: {result1}")
        if result1:
            print("Grille après multiplication (replace=true):")
            print(executor.grid)
        else:
            print(f"Erreur: {executor.error}")
    
    # Réinitialiser la grille
    executor.grid = np.array([
        [1, 2, 0, 0, 0, 0],
        [3, 4, 0, 0, 0, 0],
        [0, 0, 0, 0, 0, 0],
        [0, 0, 0, 0, 0, 0],
        [0, 0, 0, 0, 0, 0],
        [0, 0, 0, 0, 0, 0]
    ])
    
    # Test 2: MULTIPLY avec replace=false
    print("\n--- Test 2: MULTIPLY 2 false [0,0 1,1] ---")
    cmd2 = UnifiedCommand.parse("MULTIPLY 2 false [0,0 1,1]")
    if cmd2:
        result2 = executor._cmd_multiply(cmd2)
        print(f"Résultat: {result2}")
        if result2:
            print("Grille après multiplication (replace=false):")
            print(executor.grid)
        else:
            print(f"Erreur: {executor.error}")
    
    return result1 and result2


def test_divide_with_validation():
    """Test de DIVIDE avec validation des blocs identiques"""
    print("\n🧪 Test de DIVIDE avec validation des blocs")
    
    executor = CommandExecutor()
    
    # Test 1: Grille avec blocs identiques (devrait réussir)
    print("\n--- Test 1: Blocs identiques 2x2 ---")
    executor.grid = np.array([
        [1, 2, 1, 2],
        [3, 4, 3, 4],
        [1, 2, 1, 2],
        [3, 4, 3, 4]
    ])
    executor.height, executor.width = executor.grid.shape
    
    print("Grille avec blocs identiques:")
    print(executor.grid)
    
    cmd1 = UnifiedCommand.parse("DIVIDE 2 [0,0 3,3]")
    if cmd1:
        result1 = executor._cmd_divide(cmd1)
        print(f"Résultat: {result1}")
        if result1:
            print("Grille après division:")
            print(executor.grid)
        else:
            print(f"Erreur: {executor.error}")
    
    # Test 2: Grille avec blocs différents (devrait échouer)
    print("\n--- Test 2: Blocs différents ---")
    executor.grid = np.array([
        [1, 2, 3, 4],
        [5, 6, 7, 8],
        [9, 1, 2, 3],
        [4, 5, 6, 7]
    ])
    executor.height, executor.width = executor.grid.shape
    
    print("Grille avec blocs différents:")
    print(executor.grid)
    
    cmd2 = UnifiedCommand.parse("DIVIDE 2 [0,0 3,3]")
    if cmd2:
        result2 = executor._cmd_divide(cmd2)
        print(f"Résultat: {result2} (attendu: False)")
        if not result2:
            print(f"Erreur attendue: {executor.error}")
        else:
            print("⚠️ La division a réussi alors qu'elle devrait échouer")
    
    return result1 and not result2


def test_multiply_pattern_repetition():
    """Test que MULTIPLY répète correctement le motif"""
    print("\n🧪 Test de répétition du motif avec MULTIPLY")
    
    executor = CommandExecutor()
    executor.grid = np.array([
        [1, 0, 2, 0, 0, 0, 0, 0, 0],
        [0, 0, 0, 0, 0, 0, 0, 0, 0],
        [3, 0, 4, 0, 0, 0, 0, 0, 0],
        [0, 0, 0, 0, 0, 0, 0, 0, 0],
        [0, 0, 0, 0, 0, 0, 0, 0, 0],
        [0, 0, 0, 0, 0, 0, 0, 0, 0],
        [0, 0, 0, 0, 0, 0, 0, 0, 0],
        [0, 0, 0, 0, 0, 0, 0, 0, 0],
        [0, 0, 0, 0, 0, 0, 0, 0, 0]
    ])
    executor.height, executor.width = executor.grid.shape
    
    print("Grille initiale avec motif 3x3:")
    print(executor.grid[:3, :3])
    
    # Multiplier le motif 3x3 par 3
    cmd = UnifiedCommand.parse("MULTIPLY 3 [0,0 2,2]")
    if cmd:
        result = executor._cmd_multiply(cmd)
        print(f"Résultat: {result}")
        if result:
            print("Grille après multiplication par 3:")
            print(executor.grid)
            
            # Vérifier que le motif est répété correctement
            original_pattern = np.array([[1, 0, 2], [0, 0, 0], [3, 0, 4]])
            
            # Vérifier chaque bloc 3x3 dans la grille 9x9
            pattern_correct = True
            for i in range(3):
                for j in range(3):
                    start_row = i * 3
                    end_row = start_row + 3
                    start_col = j * 3
                    end_col = start_col + 3
                    
                    block = executor.grid[start_row:end_row, start_col:end_col]
                    if not np.array_equal(block, original_pattern):
                        pattern_correct = False
                        print(f"❌ Bloc ({i},{j}) incorrect:")
                        print(block)
            
            if pattern_correct:
                print("✅ Le motif est correctement répété dans tous les blocs")
            else:
                print("❌ Le motif n'est pas correctement répété")
                
            return pattern_correct
        else:
            print(f"Erreur: {executor.error}")
            return False
    
    return False


if __name__ == "__main__":
    print("=== Test des commandes MULTIPLY et DIVIDE améliorées ===")
    
    success1 = test_multiply_with_replace_option()
    success2 = test_divide_with_validation()
    success3 = test_multiply_pattern_repetition()
    
    print(f"\n📊 Résumé:")
    print(f"  - MULTIPLY avec option replace: {'✅' if success1 else '❌'}")
    print(f"  - DIVIDE avec validation: {'✅' if success2 else '❌'}")
    print(f"  - MULTIPLY répétition motif: {'✅' if success3 else '❌'}")
    
    if success1 and success2 and success3:
        print("\n🎉 Tous les tests sont réussis!")
    else:
        print("\n💥 Certains tests ont échoué")
