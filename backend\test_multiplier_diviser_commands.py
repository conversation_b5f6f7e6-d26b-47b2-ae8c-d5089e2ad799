#!/usr/bin/env python
"""
Test des nouvelles commandes MULTIPLY et DIVIDE
"""

import os
import sys
import django
import numpy as np

# Configuration Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from command_system.command_executor import CommandExecutor
from command_system.unified_command import UnifiedCommand


def test_multiply_command():
    """Test de la commande MULTIPLY"""
    print("🧪 Test de la commande MULTIPLY")
    
    # Créer un exécuteur avec une grille de test 4x4
    executor = CommandExecutor()
    executor.grid = np.array([
        [1, 2, 0, 0],
        [3, 4, 0, 0],
        [0, 0, 0, 0],
        [0, 0, 0, 0]
    ])
    executor.height, executor.width = executor.grid.shape
    
    print("Grille initiale:")
    print(executor.grid)
    
    # Test 1: Multiplier une région 2x2 par 2
    print("\n--- Test 1: MULTIPLY 2 [0,0 1,1] ---")
    cmd = UnifiedCommand.parse("MULTIPLY 2 [0,0 1,1]")
    if cmd:
        result = executor._cmd_multiply(cmd)
        print(f"Résultat: {result}")
        if result:
            print("Grille après multiplication:")
            print(executor.grid)
        else:
            print(f"Erreur: {executor.error}")
    
    return result


def test_divide_command():
    """Test de la commande DIVIDE"""
    print("\n🧪 Test de la commande DIVIDE")
    
    # Créer un exécuteur avec une grille de test 4x4
    executor = CommandExecutor()
    executor.grid = np.array([
        [1, 1, 2, 2],
        [1, 1, 2, 2],
        [3, 3, 4, 4],
        [3, 3, 4, 4]
    ])
    executor.height, executor.width = executor.grid.shape
    
    print("Grille initiale:")
    print(executor.grid)
    
    # Test 1: Diviser une région 4x4 par 2
    print("\n--- Test 1: DIVIDE 2 [0,0 3,3] ---")
    cmd = UnifiedCommand.parse("DIVIDE 2 [0,0 3,3]")
    if cmd:
        result = executor._cmd_divide(cmd)
        print(f"Résultat: {result}")
        if result:
            print("Grille après division:")
            print(executor.grid)
        else:
            print(f"Erreur: {executor.error}")
    
    return result


def test_multiply_divide_cycle():
    """Test du cycle multiplication -> division"""
    print("\n🧪 Test du cycle MULTIPLY -> DIVIDE")
    
    # Créer un exécuteur avec une grille de test 2x2
    executor = CommandExecutor()
    executor.grid = np.array([
        [1, 2, 0, 0],
        [3, 4, 0, 0],
        [0, 0, 0, 0],
        [0, 0, 0, 0]
    ])
    executor.height, executor.width = executor.grid.shape
    
    print("Grille initiale:")
    print(executor.grid)
    
    # Étape 1: Multiplier par 2
    print("\n--- Étape 1: MULTIPLY 2 [0,0 1,1] ---")
    cmd1 = UnifiedCommand.parse("MULTIPLY 2 [0,0 1,1]")
    if cmd1:
        result1 = executor._cmd_multiply(cmd1)
        print(f"Résultat multiplication: {result1}")
        if result1:
            print("Grille après multiplication:")
            print(executor.grid)
        else:
            print(f"Erreur: {executor.error}")
            return False
    
    # Étape 2: Diviser par 2 pour revenir à l'original
    print("\n--- Étape 2: DIVIDE 2 [0,0 3,3] ---")
    cmd2 = UnifiedCommand.parse("DIVIDE 2 [0,0 3,3]")
    if cmd2:
        result2 = executor._cmd_divide(cmd2)
        print(f"Résultat division: {result2}")
        if result2:
            print("Grille après division:")
            print(executor.grid)
        else:
            print(f"Erreur: {executor.error}")
            return False
    
    return result1 and result2


def test_error_cases():
    """Test des cas d'erreur"""
    print("\n🧪 Test des cas d'erreur")
    
    executor = CommandExecutor()
    executor.grid = np.array([
        [1, 2],
        [3, 4]
    ])
    executor.height, executor.width = executor.grid.shape
    
    print("Grille de test 2x2:")
    print(executor.grid)
    
    # Test 1: Facteur invalide
    print("\n--- Test 1: Facteur invalide (1) ---")
    cmd1 = UnifiedCommand.parse("MULTIPLY 1 [0,0 1,1]")
    if cmd1:
        result1 = executor._cmd_multiply(cmd1)
        print(f"Résultat: {result1} (attendu: False)")
        print(f"Erreur: {executor.error}")
    
    # Test 2: Multiplication qui dépasse les limites
    print("\n--- Test 2: Multiplication qui dépasse les limites ---")
    cmd2 = UnifiedCommand.parse("MULTIPLY 3 [0,0 1,1]")
    if cmd2:
        result2 = executor._cmd_multiply(cmd2)
        print(f"Résultat: {result2} (attendu: False)")
        print(f"Erreur: {executor.error}")
    
    # Test 3: Division non divisible
    print("\n--- Test 3: Division non divisible ---")
    cmd3 = UnifiedCommand.parse("DIVIDE 3 [0,0 1,1]")
    if cmd3:
        result3 = executor._cmd_divide(cmd3)
        print(f"Résultat: {result3} (attendu: False)")
        print(f"Erreur: {executor.error}")
    
    return not result1 and not result2 and not result3


def main():
    """Fonction principale de test"""
    print("=== Test des commandes MULTIPLY et DIVIDE ===")

    tests = [
        ("MULTIPLY", test_multiply_command),
        ("DIVIDE", test_divide_command),
        ("Cycle MULTIPLY->DIVIDE", test_multiply_divide_cycle),
        ("Cas d'erreur", test_error_cases)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
            print(f"\n✅ Test {test_name}: {'RÉUSSI' if result else 'ÉCHOUÉ'}")
        except Exception as e:
            results.append((test_name, False))
            print(f"\n❌ Test {test_name}: ERREUR - {e}")
    
    print("\n=== Résumé des tests ===")
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ RÉUSSI" if result else "❌ ÉCHOUÉ"
        print(f"{test_name}: {status}")
    
    print(f"\nRésultat global: {passed}/{total} tests réussis")
    
    if passed == total:
        print("🎉 Tous les tests sont passés !")
        return True
    else:
        print("⚠️ Certains tests ont échoué")
        return False


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
